from datetime import datetime
from flask import jsonify, request, make_response
from flask_login import login_required, current_user
from bson.errors import InvalidId
from bson import ObjectId

from models.thread import Thread
from models.shared_thread import SharedThread
from . import chat_api
from utils.decorators import check_service_access, check_thread_limit
from utils.api_logger import log_api_request
import logging

@chat_api.route('/threads', methods=['GET'])
@login_required
@check_service_access('chat')
def get_threads():
    """Get all threads for the current user (summary only)"""
    # Only fetch the fields we need, exclude messages completely
    threads = Thread.objects(user=current_user.id).only('id', 'title', 'created_at', 'updated_at').order_by('-updated_at')
    
    # Get message counts in a separate efficient query
    thread_ids = [str(thread.id) for thread in threads]
    message_counts = {}
    
    # Use MongoDB aggregation to get message counts efficiently
    pipeline = [
        {'$match': {'_id': {'$in': [Thread.id.to_mongo(tid) for tid in thread_ids]}}},
        {'$project': {'_id': 1, 'message_count': {'$size': '$messages'}}}
    ]
    
    # Execute the aggregation pipeline
    message_count_results = Thread._get_collection().aggregate(pipeline)
    
    # Create a mapping of thread ID to message count
    for result in message_count_results:
        message_counts[str(result['_id'])] = result['message_count']
    
    # Format the response
    response = []
    for thread in threads:
        thread_id = str(thread.id)
        response.append({
            'id': thread_id,
            'title': thread.title,
            'created_at': thread.created_at.isoformat(),
            'updated_at': thread.updated_at.isoformat(),
            'message_count': message_counts.get(thread_id, 0)
        })
    
    # Create response with cache headers (5 seconds cache)
    resp = make_response(jsonify(response))
    resp.headers['Cache-Control'] = 'private, max-age=5'
    return resp

@chat_api.route('/thread/<thread_id>', methods=['GET'])
@login_required
@check_service_access('chat')
def get_thread(thread_id):
    """Get a specific thread by ID"""
    try:
        # Check if this might be a shared thread ID (not a valid ObjectId)
        try:
            # Try to convert to ObjectId to validate
            ObjectId(thread_id)
            is_valid_object_id = True
        except InvalidId:
            is_valid_object_id = False
            
        # If it's not a valid ObjectId, check if it's a shared thread
        if not is_valid_object_id:
            # Redirect to the shared thread endpoint
            shared_thread = SharedThread.objects(share_id=thread_id).first()
            if shared_thread:
                # Return the shared thread data
                response = shared_thread.to_dict()
                resp = make_response(jsonify(response))
                resp.headers['Cache-Control'] = 'private, max-age=30'
                return resp
            else:
                return jsonify({'error': 'Thread not found'}), 404
        
        # First update the timestamp directly in the database
        now = datetime.utcnow()
        Thread.objects(id=thread_id, user=current_user.id).update(set__updated_at=now)
        
        # Then fetch the thread with the updated timestamp
        thread = Thread.objects(id=thread_id, user=current_user.id).only(
            'id', 'title', 'messages', 'created_at', 'updated_at', 'participants'
        ).first()
        
        if not thread:
            return jsonify({'error': 'Thread not found'}), 404
        
        # Prepare response directly without using to_dict for better performance
        response = {
            'id': str(thread.id),
            'title': thread.title,
            'created_at': thread.created_at.isoformat(),
            'updated_at': thread.updated_at.isoformat(),
            'messages': thread.messages,
            'participants': thread.participants
        }
        
        # Create response with cache headers (30 seconds cache for individual threads)
        resp = make_response(jsonify(response))
        resp.headers['Cache-Control'] = 'private, max-age=30'
        return resp
    except Exception as e:
        logging.error(f"Error fetching thread {thread_id}: {str(e)}")
        return jsonify({'error': 'Failed to load thread'}), 500

@chat_api.route('/thread', methods=['POST'])
@login_required
@check_service_access('chat')
@check_thread_limit()
@log_api_request('thread_create', 'chat')
def create_thread():
    """Create a new thread"""
    thread = Thread(user=current_user.id).save()
    return jsonify(thread.to_dict())

@chat_api.route('/thread/<thread_id>', methods=['DELETE'])
@login_required
@check_service_access('chat')
def delete_thread(thread_id):
    """Delete a thread"""
    thread = Thread.objects(id=thread_id, user=current_user.id).first()
    if not thread:
        return jsonify({'error': 'Thread not found'}), 404
    thread.delete()
    return jsonify({'success': True})

@chat_api.route('/thread/<thread_id>/title', methods=['PUT'])
@login_required
@check_service_access('chat')
def update_thread_title(thread_id):
    """Update thread title"""
    data = request.json
    new_title = data.get('title')

    if not new_title:
        return jsonify({'error': 'Title is required'}), 400

    thread = Thread.objects(id=thread_id, user=current_user.id).first()
    if not thread:
        return jsonify({'error': 'Thread not found'}), 404

    thread.title = new_title
    thread.save()

    return jsonify({'success': True, 'thread': thread.to_dict()})