# This file makes the utils directory a Python package
# Import commonly used utilities for easier access

from utils.logger import setup_logger
from utils.cache import cache
from utils.decorators import admin_required, login_required, api_login_required

# Import the socketio middleware if needed
try:
    from utils.socketio_middleware import init_socketio_middleware
except ImportError:
    # This is fine if Socket.IO is not being used
    pass
