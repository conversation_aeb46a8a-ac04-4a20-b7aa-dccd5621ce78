"""
User Presence WebSocket Handlers
Handles real-time presence updates via WebSocket connections
"""

from flask_socketio import emit, join_room, leave_room, disconnect
from flask_login import current_user
from flask import request
from models.user_presence import UserPresence
from models.friend_relationship import FriendRelationship
from utils.cache import cache
import logging

# Import Socket.IO monitoring utilities if available
try:
    from utils.socketio_monitor import monitor_socketio_event, record_connection
    _monitoring_enabled = True
except ImportError:
    # Create dummy decorators if monitoring is not available
    def monitor_socketio_event(event_name):
        def decorator(func):
            return func
        return decorator
    def record_connection():
        pass
    _monitoring_enabled = False

def init_presence_socketio(socketio):
    """Initialize presence WebSocket event handlers"""
    
    # Create a namespace-specific handler class to avoid conflicts
    class PresenceNamespace:
        @staticmethod
        @socketio.on('connect', namespace='/presence')
        @monitor_socketio_event('presence_connect')
        def handle_presence_connect():
            """Handle client connection to presence namespace"""
            # Record connection for monitoring
            record_connection()
            if not current_user.is_authenticated:
                logging.warning(f"Unauthenticated user attempted to connect to presence namespace: {request.sid}")
                disconnect()
                return False
            
            try:
                # Mark user as online with this session ID
                UserPresence.set_user_online(current_user, request.sid)
                
                # Join user's personal room for receiving status updates
                user_room = f"user_{current_user.id}"
                join_room(user_room, namespace='/presence')
                
                # Clear any cached status for this user
                cache_key = f'presence_status:{current_user.username}'
                cache.delete(cache_key)
                
                # Notify friends that this user is now online
                notify_friends_status_change(current_user, True)
                
                # Send confirmation to the client
                emit('presence_connected', {
                    'success': True,
                    'message': 'Connected to presence monitoring',
                    'user_id': str(current_user.id),
                    'username': current_user.username
                })
                
                logging.info(f"User {current_user.username} connected to presence namespace with session {request.sid}")
                return True
                
            except Exception as e:
                logging.error(f"Error handling presence connect for user {current_user.username if current_user.is_authenticated else 'unknown'}: {str(e)}")
                emit('presence_error', {
                    'error': 'Failed to establish presence connection'
                })
                disconnect()
                return False
        
        @staticmethod
        @socketio.on('disconnect', namespace='/presence')
        @monitor_socketio_event('presence_disconnect')
        def handle_presence_disconnect():
            """Handle client disconnection from presence namespace"""
            if not current_user.is_authenticated:
                return
            
            try:
                # Mark user as offline
                UserPresence.set_user_offline(current_user, request.sid)
                
                # Clear any cached status for this user
                cache_key = f'presence_status:{current_user.username}'
                cache.delete(cache_key)
                
                # Notify friends that this user is now offline
                notify_friends_status_change(current_user, False)
                
                logging.info(f"User {current_user.username} disconnected from presence namespace")
                
            except Exception as e:
                logging.error(f"Error handling presence disconnect for user {current_user.username if current_user.is_authenticated else 'unknown'}: {str(e)}")
        
        @staticmethod
        @socketio.on('heartbeat', namespace='/presence')
        @monitor_socketio_event('presence_heartbeat')
        def handle_presence_heartbeat():
            """Handle heartbeat to keep connection alive and update last seen"""
            if not current_user.is_authenticated:
                disconnect()
                return
            
            try:
                # Update user's last seen time
                UserPresence.set_user_online(current_user, request.sid)
                
                # Send heartbeat response
                emit('heartbeat_ack', {
                    'timestamp': UserPresence.get_user_status(current_user)['last_seen']
                })
                
            except Exception as e:
                logging.error(f"Error handling heartbeat for user {current_user.username}: {str(e)}")
        
        @staticmethod
        @socketio.on('get_friends_status', namespace='/presence')
        @monitor_socketio_event('presence_get_friends_status')
        def handle_get_friends_status():
            """Handle request for friends' online status"""
            if not current_user.is_authenticated:
                disconnect()
                return
            
            try:
                # Get user's friends
                friends = FriendRelationship.get_friends(current_user.id)
                friend_ids = [friend.id for friend in friends]
                
                if not friend_ids:
                    emit('friends_status_update', {
                        'friends_status': {}
                    })
                    return
                
                # Get status for all friends
                friends_status = UserPresence.get_friends_status(friend_ids)
                
                # Convert to username-based mapping
                result = {}
                for friend in friends:
                    friend_id_str = str(friend.id)
                    if friend_id_str in friends_status:
                        result[friend.username] = friends_status[friend_id_str]
                    else:
                        result[friend.username] = {
                            'is_online': False,
                            'last_seen': None,
                            'connected_at': None
                        }
                
                emit('friends_status_update', {
                    'friends_status': result
                })
                
            except Exception as e:
                logging.error(f"Error getting friends status for user {current_user.username}: {str(e)}")
                emit('presence_error', {
                    'error': 'Failed to get friends status'
                })
    
    # Register the namespace handlers
    logging.info("Registered presence namespace handlers")

def notify_friends_status_change(user, is_online):
    """
    Notify all friends of a user's status change
    
    Args:
        user: User object whose status changed
        is_online: Boolean indicating if user is now online or offline
    """
    try:
        # Get user's friends
        friends = FriendRelationship.get_friends(user.id)
        
        if not friends:
            logging.info(f"User {user.username} has no friends to notify about status change")
            return
            
        # Create status update message
        user_status = UserPresence.get_user_status(user)
        status_update = {
            'user_id': str(user.id),
            'username': user.username,
            'is_online': is_online,
            'timestamp': user_status.get('last_seen') if user_status else None
        }
        
        # Send update to each friend's room
        socketio = get_socketio_instance()

        if socketio:
            # Use a try-except block for each friend to prevent one failure from affecting others
            for friend in friends:
                try:
                    friend_room = f"user_{friend.id}"
                    # Use emit with namespace parameter to ensure proper routing
                    socketio.emit(
                        'friend_status_change', 
                        status_update, 
                        room=friend_room, 
                        namespace='/presence'
                    )
                    logging.debug(f"Sent status update to friend {friend.username} in room {friend_room}")
                except Exception as friend_error:
                    logging.error(f"Error sending status update to friend {friend.username}: {str(friend_error)}")
                    continue
                    
            logging.info(f"Notified {len(friends)} friends of {user.username}'s status change to {'online' if is_online else 'offline'}")
        else:
            logging.warning("SocketIO instance not available for broadcasting status updates")
        
    except Exception as e:
        logging.error(f"Error notifying friends of status change for user {user.username}: {str(e)}")

def broadcast_status_update(user, is_online):
    """
    Broadcast a user's status update to all connected clients who should see it
    
    Args:
        user: User object whose status changed
        is_online: Boolean indicating if user is now online or offline
    """
    try:
        # Clear cached status for this user
        cache_key = f'presence_status:{user.username}'
        cache.delete(cache_key)
        
        # Log the status update
        logging.info(f"Broadcasting status update for user {user.username}: {'online' if is_online else 'offline'}")
        
        # Notify friends
        notify_friends_status_change(user, is_online)
        
    except Exception as e:
        logging.error(f"Error broadcasting status update for user {user.username}: {str(e)}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")

# Global function to get the socketio instance for use in other modules
_socketio_instance = None

def set_socketio_instance(socketio):
    """Set the global socketio instance for use in other modules"""
    global _socketio_instance
    _socketio_instance = socketio

def get_socketio_instance():
    """Get the global socketio instance"""
    return _socketio_instance
