<!-- Admin Credits Management Section -->
<div class="bg-slate-800/50 rounded-lg p-4 border border-slate-700/50">
    <div class="flex items-center justify-between mb-3">
        <h3 class="text-md font-medium text-slate-200 flex items-center">
            <i data-lucide="coins" class="h-4 w-4 mr-2 text-amber-400"></i>
            Credit Management
        </h3>
        <div class="flex space-x-2">
            <button id="addCreditsBtn" class="text-xs bg-slate-700 hover:bg-slate-600 text-slate-300 px-2 py-1 rounded flex items-center">
                <i data-lucide="edit" class="h-3 w-3 mr-1"></i>
                Manage Credits
            </button>
            <button id="refreshUserCreditsBtn" class="text-xs bg-slate-700 hover:bg-slate-600 text-slate-300 px-2 py-1 rounded flex items-center">
                <i data-lucide="refresh-cw" class="h-3 w-3 mr-1"></i>
                Refresh
            </button>
        </div>
    </div>

    <!-- User Credits Table -->
    <div id="userCreditsTable" class="mb-4 overflow-x-auto">
        <div class="flex justify-center items-center py-8">
            <div id="usersLoadingIndicator" class="flex items-center">
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-cyan-500 mr-3"></div>
                <span class="text-slate-400">Loading user credits...</span>
            </div>
        </div>
    </div>
</div>

<!-- Model Costs Management Section -->
<div class="bg-slate-800/50 rounded-lg p-4 border border-slate-700/50 mt-4">
    <div class="flex items-center justify-between mb-3">
        <h3 class="text-md font-medium text-slate-200 flex items-center">
            <i data-lucide="settings" class="h-4 w-4 mr-2 text-cyan-400"></i>
            Model Cost Settings
        </h3>
        <div class="flex space-x-2">
            <button id="updateModelCostBtn" class="text-xs bg-slate-700 hover:bg-slate-600 text-slate-300 px-2 py-1 rounded flex items-center">
                <i data-lucide="edit" class="h-3 w-3 mr-1"></i>
                Edit Cost
            </button>
            <button id="initializeDefaultCostsBtn" class="text-xs bg-slate-700 hover:bg-slate-600 text-slate-300 px-2 py-1 rounded flex items-center">
                <i data-lucide="refresh-cw" class="h-3 w-3 mr-1"></i>
                Reset Defaults
            </button>
            <button id="refreshModelCostsBtn" class="text-xs bg-slate-700 hover:bg-slate-600 text-slate-300 px-2 py-1 rounded flex items-center">
                <i data-lucide="refresh-cw" class="h-3 w-3 mr-1"></i>
                Refresh
            </button>
        </div>
    </div>

    <!-- Model Costs Table -->
    <div id="modelCostsContainer" class="overflow-x-auto">
        <div class="flex justify-center items-center py-8">
            <div id="modelsLoadingIndicator" class="flex items-center">
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-cyan-500 mr-3"></div>
                <span class="text-slate-400">Loading model costs...</span>
            </div>
        </div>
    </div>
</div>

<!-- Add Credits Modal Template -->
<div id="addCreditsModal" class="fixed inset-0 bg-black/70 flex items-center justify-center z-50 hidden">
    <div class="bg-slate-800 rounded-lg border border-slate-700 w-full max-w-md">
        <div class="flex items-center justify-between p-4 border-b border-slate-700">
            <h3 class="text-lg font-medium text-slate-200">Manage User Credits</h3>
            <button id="closeAddCreditsModal" class="text-slate-400 hover:text-slate-200">
                <i data-lucide="x" class="h-5 w-5"></i>
            </button>
        </div>
        <div class="p-4">
            <div class="mb-4">
                <label for="creditUserSearch" class="block text-sm font-medium text-slate-300 mb-1">Search User</label>
                <input type="text" id="creditUserSearch" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500" placeholder="Enter username or email (min 3 chars)">
                <div id="userSearchResults" class="mt-2 max-h-40 overflow-y-auto bg-slate-800 rounded-md border border-slate-700"></div>
                <select id="creditUser" class="hidden w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500 mt-2">
                    <option value="">Select a user</option>
                </select>
            </div>
            <div class="mb-4">
                <label for="creditAmount" class="block text-sm font-medium text-slate-300 mb-1">Amount (use negative values to remove credits)</label>
                <input type="number" id="creditAmount" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500" value="10">
                <p class="text-xs text-slate-400 mt-1">Maximum user balance: 1000 credits</p>
            </div>
            <div class="mb-4">
                <label for="creditReason" class="block text-sm font-medium text-slate-300 mb-1">Reason</label>
                <input type="text" id="creditReason" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500" placeholder="Admin adjustment">
            </div>
        </div>
        <div class="flex justify-end p-4 border-t border-slate-700">
            <button id="cancelAddCredits" class="px-4 py-2 bg-slate-700 text-slate-300 rounded-md hover:bg-slate-600 mr-2">Cancel</button>
            <button id="confirmAddCredits" class="px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-500">Update Credits</button>
        </div>
    </div>
</div>

<!-- Update Model Cost Modal Template -->
<div id="updateModelCostModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black/70 hidden">
    <div class="bg-slate-800 rounded-lg border border-slate-700 w-full max-w-md">
        <div class="flex items-center justify-between p-4 border-b border-slate-700">
            <h3 class="text-lg font-medium text-slate-200">Update Model Cost</h3>
            <button id="closeUpdateModelCostModal" class="text-slate-400 hover:text-slate-200">
                <i data-lucide="x" class="h-5 w-5"></i>
            </button>
        </div>
        <div class="p-4">
            <div class="mb-4">
                <label for="modelName" class="block text-sm font-medium text-slate-300 mb-1">Model</label>
                <select id="modelName" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    <option value="">Select a model</option>
                    <!-- Model options will be populated dynamically -->
                </select>
            </div>
            <div class="mb-4">
                <label for="modelCost" class="block text-sm font-medium text-slate-300 mb-1">Credit Cost</label>
                <input type="number" id="modelCost" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500" min="1" value="1">
            </div>
        </div>
        <div class="flex justify-end p-4 border-t border-slate-700">
            <button id="cancelUpdateModelCost" class="px-4 py-2 bg-slate-700 text-slate-300 rounded-md hover:bg-slate-600 mr-2">Cancel</button>
            <button id="confirmUpdateModelCost" class="px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-500">Update Cost</button>
        </div>
    </div>
</div>
