"""
Performance monitoring routes for Friends API
Provides endpoints to monitor and analyze API performance
"""

from flask import jsonify, request
from flask_login import login_required, current_user
from .. import friends_api
from utils.decorators import check_service_access
from utils.performance_monitor import get_performance_summary, performance_monitor
from utils.redis_cache import cache
import logging
from datetime import datetime, timedelta


@friends_api.route('/performance/stats', methods=['GET'])
@login_required
@check_service_access('friends')
def get_performance_stats():
    """Get performance statistics for Friends API"""
    # Check if user is admin
    if not current_user.is_admin:
        return jsonify({'error': 'Unauthorized - Admin access required'}), 403
    
    try:
        # Get overall performance summary
        summary = get_performance_summary()
        
        # Get specific metrics for friends endpoints
        metrics = performance_monitor.get_metrics()
        friends_metrics = {
            key: value for key, value in metrics.items() 
            if 'friends' in key.lower() or 'chat' in key.lower()
        }
        
        # Get cache statistics
        cache_stats = cache.get_stats()
        
        # Calculate performance improvements
        improvements = calculate_performance_improvements()
        
        return jsonify({
            'success': True,
            'timestamp': datetime.utcnow().isoformat(),
            'overall_summary': summary,
            'friends_metrics': friends_metrics,
            'cache_stats': cache_stats,
            'performance_improvements': improvements,
            'recommendations': get_performance_recommendations(friends_metrics)
        })
        
    except Exception as e:
        logging.error(f"Error getting performance stats: {str(e)}")
        return jsonify({'error': 'Failed to retrieve performance statistics'}), 500


@friends_api.route('/performance/cache/stats', methods=['GET'])
@login_required
@check_service_access('friends')
def get_cache_stats():
    """Get detailed cache statistics"""
    # Check if user is admin
    if not current_user.is_admin:
        return jsonify({'error': 'Unauthorized - Admin access required'}), 403
    
    try:
        cache_stats = cache.get_stats()
        
        # Get cache hit/miss ratios for specific endpoints
        endpoint_cache_stats = {}
        
        # Sample recent cache keys to analyze patterns
        sample_keys = []
        try:
            if hasattr(cache, 'redis_client') and cache.redis_client:
                # Get sample of cache keys
                sample_keys = cache.redis_client.keys('*')[:100]  # Limit to 100 keys
        except Exception as e:
            logging.warning(f"Could not sample cache keys: {str(e)}")
        
        return jsonify({
            'success': True,
            'timestamp': datetime.utcnow().isoformat(),
            'cache_stats': cache_stats,
            'endpoint_stats': endpoint_cache_stats,
            'sample_keys': sample_keys[:20],  # Return only first 20 for display
            'total_sampled_keys': len(sample_keys)
        })
        
    except Exception as e:
        logging.error(f"Error getting cache stats: {str(e)}")
        return jsonify({'error': 'Failed to retrieve cache statistics'}), 500


@friends_api.route('/performance/cache/clear', methods=['POST'])
@login_required
@check_service_access('friends')
def clear_cache():
    """Clear cache for Friends API (admin only)"""
    # Check if user is admin
    if not current_user.is_admin:
        return jsonify({'error': 'Unauthorized - Admin access required'}), 403
    
    try:
        data = request.json or {}
        pattern = data.get('pattern', 'friends:*')
        
        # Clear cache based on pattern
        deleted_count = cache.delete_pattern(pattern)
        
        logging.info(f"Admin {current_user.username} cleared cache with pattern '{pattern}', deleted {deleted_count} keys")
        
        return jsonify({
            'success': True,
            'message': f'Cleared {deleted_count} cache entries',
            'pattern': pattern,
            'deleted_count': deleted_count,
            'timestamp': datetime.utcnow().isoformat()
        })
        
    except Exception as e:
        logging.error(f"Error clearing cache: {str(e)}")
        return jsonify({'error': 'Failed to clear cache'}), 500


@friends_api.route('/performance/benchmark', methods=['POST'])
@login_required
@check_service_access('friends')
def run_performance_benchmark():
    """Run performance benchmark tests (admin only)"""
    # Check if user is admin
    if not current_user.is_admin:
        return jsonify({'error': 'Unauthorized - Admin access required'}), 403
    
    try:
        import time
        from models.friend_relationship import FriendRelationship
        from models.friend_chat import FriendChat
        
        benchmark_results = {}
        
        # Benchmark 1: Friends list query
        start_time = time.time()
        friends = FriendRelationship.get_friends(current_user.id)
        friends_query_time = (time.time() - start_time) * 1000
        benchmark_results['friends_list_query_ms'] = friends_query_time
        
        # Benchmark 2: Chat queries
        start_time = time.time()
        user_chats = list(FriendChat.objects(user1=current_user.id)) + list(FriendChat.objects(user2=current_user.id))
        chat_query_time = (time.time() - start_time) * 1000
        benchmark_results['chat_queries_ms'] = chat_query_time
        
        # Benchmark 3: Cache operations
        start_time = time.time()
        test_key = f"benchmark_test:{current_user.id}:{int(time.time())}"
        test_data = {'test': 'data', 'timestamp': datetime.utcnow().isoformat()}
        cache.set(test_key, test_data, ttl=60)
        cached_data = cache.get(test_key)
        cache.delete(test_key)
        cache_operation_time = (time.time() - start_time) * 1000
        benchmark_results['cache_operations_ms'] = cache_operation_time
        
        # Overall assessment
        total_time = friends_query_time + chat_query_time + cache_operation_time
        benchmark_results['total_benchmark_time_ms'] = total_time
        
        # Performance rating
        if total_time < 100:
            performance_rating = 'Excellent'
        elif total_time < 500:
            performance_rating = 'Good'
        elif total_time < 1000:
            performance_rating = 'Fair'
        else:
            performance_rating = 'Needs Improvement'
        
        benchmark_results['performance_rating'] = performance_rating
        benchmark_results['friends_count'] = len(friends)
        benchmark_results['chats_count'] = len(user_chats)
        
        logging.info(f"Performance benchmark completed by {current_user.username}: {performance_rating} ({total_time:.2f}ms)")
        
        return jsonify({
            'success': True,
            'benchmark_results': benchmark_results,
            'timestamp': datetime.utcnow().isoformat(),
            'user_id': str(current_user.id)
        })
        
    except Exception as e:
        logging.error(f"Error running performance benchmark: {str(e)}")
        return jsonify({'error': 'Failed to run performance benchmark'}), 500


def calculate_performance_improvements():
    """Calculate performance improvements from optimizations"""
    try:
        # These would be baseline measurements before optimization
        baseline_metrics = {
            'friends_list_avg_ms': 2000,  # 2 seconds before optimization
            'chat_messages_avg_ms': 2000,  # 2 seconds before optimization
            'cache_hit_rate': 0  # No caching before
        }
        
        # Get current metrics
        current_metrics = performance_monitor.get_metrics()
        
        improvements = {}
        
        # Calculate improvements for friends list
        friends_metric = current_metrics.get('get_friends', {})
        if friends_metric.get('avg_time_ms'):
            improvement = ((baseline_metrics['friends_list_avg_ms'] - friends_metric['avg_time_ms']) / baseline_metrics['friends_list_avg_ms']) * 100
            improvements['friends_list_improvement_percent'] = max(0, improvement)
        
        # Calculate improvements for chat messages
        chat_metric = current_metrics.get('get_chat_messages', {})
        if chat_metric.get('avg_time_ms'):
            improvement = ((baseline_metrics['chat_messages_avg_ms'] - chat_metric['avg_time_ms']) / baseline_metrics['chat_messages_avg_ms']) * 100
            improvements['chat_messages_improvement_percent'] = max(0, improvement)
        
        # Cache hit rate improvement
        cache_stats = cache.get_stats()
        current_hit_rate = cache_stats.get('hit_rate', 0)
        improvements['cache_hit_rate'] = current_hit_rate
        improvements['cache_enabled'] = cache_stats.get('enabled', False)
        
        return improvements
        
    except Exception as e:
        logging.error(f"Error calculating performance improvements: {str(e)}")
        return {}


def get_performance_recommendations(metrics):
    """Get performance recommendations based on metrics"""
    recommendations = []
    
    try:
        # Check for slow endpoints
        for endpoint, data in metrics.items():
            avg_time = data.get('avg_time_ms', 0)
            slow_query_rate = data.get('slow_query_rate', 0)
            
            if avg_time > 500:  # Slower than 500ms
                recommendations.append({
                    'type': 'warning',
                    'endpoint': endpoint,
                    'message': f'Endpoint {endpoint} is averaging {avg_time:.2f}ms response time',
                    'suggestion': 'Consider additional caching or query optimization'
                })
            
            if slow_query_rate > 10:  # More than 10% slow queries
                recommendations.append({
                    'type': 'warning',
                    'endpoint': endpoint,
                    'message': f'Endpoint {endpoint} has {slow_query_rate:.1f}% slow queries',
                    'suggestion': 'Investigate database indexes and query patterns'
                })
        
        # Check cache performance
        cache_stats = cache.get_stats()
        if cache_stats.get('enabled'):
            hit_rate = cache_stats.get('hit_rate', 0)
            if hit_rate < 0.7:  # Less than 70% hit rate
                recommendations.append({
                    'type': 'info',
                    'message': f'Cache hit rate is {hit_rate:.1%}',
                    'suggestion': 'Consider increasing cache TTL or improving cache key strategies'
                })
        else:
            recommendations.append({
                'type': 'error',
                'message': 'Cache is not enabled',
                'suggestion': 'Enable Redis caching for better performance'
            })
        
        # Add general recommendations
        if not recommendations:
            recommendations.append({
                'type': 'success',
                'message': 'All performance metrics look good!',
                'suggestion': 'Continue monitoring for any performance degradation'
            })
        
        return recommendations
        
    except Exception as e:
        logging.error(f"Error generating recommendations: {str(e)}")
        return [{'type': 'error', 'message': 'Could not generate recommendations'}]
