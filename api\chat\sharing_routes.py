from flask import jsonify, request, make_response
from flask_login import login_required, current_user

from models.user import User
from models.thread import Thread
from models.shared_thread import SharedThread
from . import chat_api
from utils.decorators import check_service_access, check_thread_limit
import logging

@chat_api.route('/shared-threads', methods=['GET'])
@login_required
@check_service_access('chat')
def get_shared_threads():
    """Get all shared threads for the current user"""
    shared_threads = SharedThread.objects(shared_with=current_user.id)
    return jsonify([st.to_dict() for st in shared_threads])

@chat_api.route('/share-thread', methods=['POST'])
@login_required
@check_service_access('chat')
def share_thread():
    """Share a thread with another user"""
    data = request.json
    thread_id = data.get('thread_id')
    username = data.get('username')

    if not thread_id or not username:
        return jsonify({'error': 'Thread ID and username are required'}), 400

    # Check if thread exists and belongs to current user
    thread = Thread.objects(id=thread_id, user=current_user.id).first()
    if not thread:
        return jsonify({'error': 'Thread not found or you do not have permission to share it'}), 404

    # Check if user to share with exists
    user = User.objects(username=username).first()
    if not user:
        return jsonify({'error': 'User not found'}), 404

    # Check if already shared
    existing_share = SharedThread.objects(thread=thread, shared_with=user).first()
    if existing_share:
        return jsonify({'error': 'Thread already shared with this user'}), 400

    # Create shared thread
    shared_thread = SharedThread(
        thread=thread,
        shared_by=current_user,
        shared_with=user
    ).save()

    return jsonify({'success': True, 'shared_thread': shared_thread.to_dict()})

@chat_api.route('/thread/<thread_id>/share', methods=['POST'])
@login_required
@check_service_access('chat')
def create_public_share_link(thread_id):
    """Create a public share link for a thread"""
    # Check if thread exists and belongs to current user
    thread = Thread.objects(id=thread_id, user=current_user.id).first()
    if not thread:
        return jsonify({'error': 'Thread not found or you do not have permission to share it'}), 404

    # Create a public shared thread
    public_thread = SharedThread(
        title=thread.title,
        messages=thread.messages
    ).save()

    # Generate share URL (relative path only, frontend will add domain)
    share_url = f"/chat/shared/{public_thread.share_id}"

    return jsonify({
        'success': True,
        'share_id': public_thread.share_id,
        'share_url': share_url
    })

@chat_api.route('/shared/<share_id>/copy', methods=['POST'])
@login_required
@check_service_access('chat')
@check_thread_limit()
def copy_shared_thread(share_id):
    """Copy a shared thread to the current user's threads"""
    # Get the shared thread
    shared_thread = SharedThread.objects(share_id=share_id).first()
    if not shared_thread:
        return jsonify({'error': 'Shared thread not found'}), 404

    # Create a new thread for the current user with the same messages
    thread = Thread(
        user=current_user.id,
        title=shared_thread.title,
        messages=shared_thread.messages
    ).save()

    return jsonify(thread.to_dict())

@chat_api.route('/shared/<share_id>', methods=['GET'])
@login_required
@check_service_access('chat')
def get_shared_thread(share_id):
    """Get a shared thread by its share_id"""
    try:
        # Find the shared thread by share_id
        shared_thread = SharedThread.objects(share_id=share_id).first()
        if not shared_thread:
            return jsonify({'error': 'Shared thread not found'}), 404
        
        # Prepare response
        response = shared_thread.to_dict()
        
        # Create response with cache headers
        resp = make_response(jsonify(response))
        resp.headers['Cache-Control'] = 'private, max-age=30'
        return resp
    except Exception as e:
        logging.error(f"Error fetching shared thread {share_id}: {str(e)}")
        return jsonify({'error': 'Failed to load shared thread'}), 500