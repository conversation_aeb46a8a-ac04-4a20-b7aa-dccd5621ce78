{% extends "base.html" %}

{% block title %}{{ user.display_name or user.username }}'s Profile{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500&family=Outfit:wght@400;500;600;700;800&family=Space+Grotesk:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* Define CSS variables with template values */
    :root {
        /* User custom values */
        --profile-bg-color: {{ profile.background_color }};
        --profile-text-color: {{ profile.text_color }};
        --profile-accent-color: {{ profile.accent_color }};
        --profile-container-color: {{ profile.profile_container_color or '#ffffff' }};
        --profile-header-color: {{ profile.profile_header_color or '#f8f9fa' }};
        --profile-about-color: {{ profile.profile_about_color or '#f8f9fa' }};
        --profile-content-color: {{ profile.profile_content_color or '#ffffff' }};
        --profile-friends-color: {{ profile.profile_friends_color or '#f8f9fa' }};
        --profile-font: {{ profile.font_family }}, 'Inter', sans-serif;
        --profile-heading-font: {{ profile.heading_font }}, 'Space Grotesk', 'Outfit', sans-serif;
        
        /* Modern vibrant color palette */
        --color-primary: #4f46e5; /* Vibrant indigo */
        --color-primary-light: #818cf8;
        --color-primary-dark: #3730a3;
        --color-secondary: #06b6d4; /* Cyan */
        --color-secondary-light: #67e8f9;
        --color-accent: #f43f5e; /* Rose */
        --color-accent-light: #fb7185;
        --color-success: #10b981; /* Emerald */
        --color-success-light: #34d399;
        --color-warning: #f59e0b; /* Amber */
        --color-warning-light: #fbbf24;
        
        /* Additional colors */
        --theme-dark: #0f172a;
        --theme-gray: #64748b;
        --theme-shadow: rgba(0, 0, 0, 0.1);
    }
    
    /* Apply background type dynamically */
    {% if profile.background_type == 'gradient' %}
    body {
        background: {{ profile.background_gradient }};
    }
    {% elif profile.background_type == 'image' %}
    body {
        background-image: url('{{ profile.background_image }}');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
    }
    {% elif profile.background_type == 'pattern' %}
    body {
        background-image: url('{{ profile.background_pattern }}');
        background-repeat: repeat;
    }
    {% else %}
    body {
        background-color: var(--theme-light);
        background-image: 
            radial-gradient(circle at 25px 25px, rgba(255, 255, 255, 0.2) 2%, transparent 0%), 
            radial-gradient(circle at 75px 75px, rgba(255, 255, 255, 0.2) 2%, transparent 0%);
        background-size: 100px 100px;
    }
    {% endif %}
    
    /* Custom CSS */
    {{ profile.custom_css|safe }}
</style>
<link rel="stylesheet" href="{{ url_for('static', filename='css/profile-page.css') }}">
{% endblock %}

{% block content %}
<div class="profile-container">
    <div class="profile-card">
        <!-- Decorative elements -->
        <div class="profile-decoration profile-decoration-1"></div>
        <div class="profile-decoration profile-decoration-2"></div>
        

        <!-- Dynamic banner with gradient -->
        <div class="profile-banner"></div>
        
        <div class="profile-header">
            <div class="profile-picture-wrapper">
                <div class="profile-picture-container">
                    <img src="{{ user.profile_picture or url_for('static', filename='images/default-avatar.png') }}" 
                         alt="{{ user.username }}" class="profile-picture" crossorigin="anonymous">
                </div>
            </div>
            
            <div class="profile-identity">
                <h1 class="profile-username">
                    {{ user.display_name or user.username }}
                </h1>
                <div class="profile-tag">@{{ user.username }}</div>
                
                <div class="profile-status-container">
                    <div class="profile-status">
                        <span class="status-dot loading"></span>
                        <span class="status-text">Loading...</span>
                    </div>
                </div>
            </div>
            
            {% if is_owner %}
            <div class="profile-actions">
                <a href="{{ url_for('profile_page.edit_profile_me') }}" class="profile-button">
                    <span class="button-icon">✏️</span>
                    <span class="button-text">Edit Profile</span>
                </a>
            </div>
            {% endif %}
            
            {% if profile.social_links %}
            <div class="social-links">
                {% for platform, link in profile.social_links.items() %}
                <a href="{{ link }}" target="_blank" rel="noopener" class="social-link" title="{{ platform }}">
                    {% set platform_lower = platform.lower() %}
                    {% if platform_lower == 'x' or platform_lower == 'twitter' %}
                        <i class="fa-brands fa-x-twitter"></i>
                    {% elif platform_lower == 'instagram' %}
                        <i class="fa-brands fa-instagram"></i>
                    {% elif platform_lower == 'facebook' %}
                        <i class="fa-brands fa-facebook"></i>
                    {% elif platform_lower == 'linkedin' %}
                        <i class="fa-brands fa-linkedin"></i>
                    {% elif platform_lower == 'github' %}
                        <i class="fa-brands fa-github"></i>
                    {% elif platform_lower == 'youtube' %}
                        <i class="fa-brands fa-youtube"></i>
                    {% elif platform_lower == 'twitch' %}
                        <i class="fa-brands fa-twitch"></i>
                    {% elif platform_lower == 'discord' %}
                        <i class="fa-brands fa-discord"></i>
                    {% elif platform_lower == 'tiktok' %}
                        <i class="fa-brands fa-tiktok"></i>
                    {% elif platform_lower == 'reddit' %}
                        <i class="fa-brands fa-reddit"></i>
                    {% else %}
                        <i class="fa-solid fa-link"></i>
                    {% endif %}
                    <span class="social-link-tooltip">{{ platform }}</span>
                </a>
                {% endfor %}
            </div>
            {% endif %}
            
            <div class="profile-stats">
                <div class="stat-item">
                    <div class="stat-value">{{ user.created_at.strftime('%b %Y') }}</div>
                    <div class="stat-label">Joined</div>
                </div>
                
                <div class="stat-divider"></div>
                
                <div class="stat-item">
                    <div class="stat-value" id="friends-count">-</div>
                    <div class="stat-label">Friends</div>
                </div>
            </div>
        </div>
        
        <div class="profile-content" style="background-color: var(--profile-content-color, var(--theme-content-bg))">
            <!-- About Section -->
            <div class="profile-section about-section">
                <div class="section-header">
                    <h2 class="profile-section-title">About Me</h2>
                </div>
                
                <div id="about-content" class="section-content">
                    {% if profile.custom_sections and profile.custom_sections.about %}
                    {{ profile.custom_sections.about|safe }}
                    {% else %}
                    <p>This user prefers to keep an air of mystery about them.</p>
                    {% endif %}
                </div>
                
                <div class="badge-container">
                    <div class="badge">Joined {{ user.created_at.strftime('%B %Y') }}</div>
                    {% if user.display_name %}
                    <div class="badge">Also known as {{ user.display_name }}</div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Friends Section -->
            {% if profile.show_friends %}
            <div class="profile-section friends-section">
                <div class="section-header">
                    <h2 class="profile-section-title">Mutual Friends</h2>
                </div>
                
                <div id="friends-content" class="section-content">
                    <div class="friends-loading">
                        <div class="loading-spinner"></div>
                        <p>Loading mutual friends...</p>
                    </div>
                </div>
            </div>
            {% endif %}
            
            <!-- Custom Sections -->
            {% if profile.custom_sections %}
            {% for section_name, section_content in profile.custom_sections.items() %}
            {% if section_name != 'about' %}
            <div class="profile-section custom-section">
                <div class="section-header">
                    <h2 class="profile-section-title">{{ section_name|title }}</h2>
                </div>
                
                <div class="section-content">
                    {{ section_content|safe }}
                </div>
            </div>
            {% endif %}
            {% endfor %}
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Create a global profileData object to pass data to the external JS file
    const profileData = {
        username: '{{ user.username }}',
        showFriends: {% if profile.show_friends %}true{% else %}false{% endif %},
        isCurrentUser: {% if current_user.is_authenticated and current_user.username == user.username %}true{% else %}false{% endif %}
    };
</script>
{% if current_user.is_authenticated %}
<!-- Socket.IO for real-time presence functionality -->
<script src="https://cdn.socket.io/4.7.2/socket.io.min.js" integrity="sha384-mZLF4UVrpi/QTWPA7BjNPEnkIfRFn4ZEO3Qt/HFklTJBj/gBOV8G3HcKn4NfQblz" crossorigin="anonymous"></script>
<script src="{{ url_for('static', filename='js/presence-manager.js') }}"></script>
{% endif %}
<script src="{{ url_for('static', filename='js/profile-page.js') }}"></script>
{% endblock %}