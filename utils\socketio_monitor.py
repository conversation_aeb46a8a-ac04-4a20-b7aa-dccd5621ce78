"""
Socket.IO performance monitoring utilities.
This module provides functions to monitor Socket.IO performance.
"""

import logging
import time
import threading
from functools import wraps

# Dictionary to store performance metrics
_socketio_metrics = {
    'event_counts': {},
    'event_times': {},
    'connection_count': 0,
    'error_count': 0,
    'last_reset': time.time()
}

# Lock for thread-safe access to metrics
_metrics_lock = threading.Lock()

def monitor_socketio_event(event_name):
    """
    Decorator to monitor Socket.IO event performance.
    
    Args:
        event_name: Name of the Socket.IO event
        
    Returns:
        Decorated function
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                # Call the original function
                result = func(*args, **kwargs)
                
                # Record metrics
                with _metrics_lock:
                    # Increment event count
                    if event_name not in _socketio_metrics['event_counts']:
                        _socketio_metrics['event_counts'][event_name] = 0
                    _socketio_metrics['event_counts'][event_name] += 1
                    
                    # Record event time
                    elapsed_time = time.time() - start_time
                    if event_name not in _socketio_metrics['event_times']:
                        _socketio_metrics['event_times'][event_name] = []
                    _socketio_metrics['event_times'][event_name].append(elapsed_time)
                    
                    # Keep only the last 100 event times
                    if len(_socketio_metrics['event_times'][event_name]) > 100:
                        _socketio_metrics['event_times'][event_name] = _socketio_metrics['event_times'][event_name][-100:]
                
                return result
                
            except Exception as e:
                # Record error
                with _metrics_lock:
                    _socketio_metrics['error_count'] += 1
                
                # Log error
                logging.error(f"Error in Socket.IO event {event_name}: {str(e)}")
                import traceback
                logging.error(f"Traceback: {traceback.format_exc()}")
                
                # Re-raise the exception
                raise
                
        return wrapper
    return decorator

def record_connection():
    """Record a new Socket.IO connection."""
    with _metrics_lock:
        _socketio_metrics['connection_count'] += 1

def get_socketio_metrics():
    """
    Get Socket.IO performance metrics.
    
    Returns:
        Dictionary with Socket.IO performance metrics
    """
    with _metrics_lock:
        # Calculate average event times
        avg_event_times = {}
        for event_name, times in _socketio_metrics['event_times'].items():
            if times:
                avg_event_times[event_name] = sum(times) / len(times)
            else:
                avg_event_times[event_name] = 0
        
        # Create metrics report
        metrics = {
            'event_counts': _socketio_metrics['event_counts'].copy(),
            'avg_event_times': avg_event_times,
            'connection_count': _socketio_metrics['connection_count'],
            'error_count': _socketio_metrics['error_count'],
            'uptime': time.time() - _socketio_metrics['last_reset']
        }
        
        return metrics

def reset_socketio_metrics():
    """Reset Socket.IO performance metrics."""
    with _metrics_lock:
        _socketio_metrics['event_counts'] = {}
        _socketio_metrics['event_times'] = {}
        _socketio_metrics['connection_count'] = 0
        _socketio_metrics['error_count'] = 0
        _socketio_metrics['last_reset'] = time.time()
        
    logging.info("Socket.IO performance metrics reset")