"""
Socket.IO initialization module.
This module provides functions to initialize Socket.IO and register event handlers.
"""

import logging
from flask_socketio import Socket<PERSON>

def create_socketio_instance(app, config=None):
    """
    Create a Socket.IO instance with optimized configuration.
    
    Args:
        app: Flask application instance
        config: Optional dictionary with Socket.IO configuration
        
    Returns:
        SocketIO instance
    """
    # Default configuration for Socket.IO
    default_config = {
        'cors_allowed_origins': "*",
        'async_mode': 'threading',
        'logger': True,
        'engineio_logger': True,
        'ping_timeout': 10,
        'ping_interval': 5,
        'manage_session': False,
        'message_queue': None,
        'max_http_buffer_size': 50 * 1024 * 1024,
        'always_connect': True,
        'websocket_ping_interval': 5,
        'websocket_ping_timeout': 10,
        'websocket_compression': False
    }
    
    # Merge default config with provided config
    if config:
        default_config.update(config)
    
    # Create Socket.IO instance
    try:
        socketio = SocketIO(app, **default_config)
        logging.info("Socket.IO instance created with configuration: %s", default_config)
        return socketio
    except Exception as e:
        logging.error("Error creating Socket.IO instance: %s", str(e))
        import traceback
        logging.error("Traceback: %s", traceback.format_exc())
        return None

def register_socketio_handlers(socketio):
    """
    Register Socket.IO event handlers in the correct order.
    
    Args:
        socketio: SocketIO instance
    """
    if not socketio:
        logging.error("Cannot register Socket.IO handlers: Socket.IO instance is None")
        return False
    
    try:
        # Set the global socketio instance for presence module first
        from api.presence.socket import set_socketio_instance
        set_socketio_instance(socketio)
        logging.info("Set global socketio instance for presence module")
        
        # Initialize socket handlers in a specific order to avoid conflicts
        # First initialize the base namespace handlers
        from blueprints.live.socket import init_socketio as init_live_socketio
        init_live_socketio(socketio)
        logging.info("Live Socket.IO handlers registered")
        
        # Then initialize the friends namespace handlers
        from api.friends.socket import init_socketio as init_friends_socketio
        init_friends_socketio(socketio)
        logging.info("Friends Socket.IO handlers registered")
        
        # Finally initialize the presence namespace handlers
        from api.presence.socket import init_presence_socketio
        init_presence_socketio(socketio)
        logging.info("Presence Socket.IO handlers registered")
        
        # Enable Socket.IO debug mode
        socketio.server.eio.logger.setLevel('DEBUG')
        logging.info("Socket.IO debug mode enabled")
        
        logging.info("Socket.IO handlers registration complete")
        return True
    except Exception as e:
        logging.error("Error registering Socket.IO handlers: %s", str(e))
        import traceback
        logging.error("Traceback: %s", traceback.format_exc())
        return False