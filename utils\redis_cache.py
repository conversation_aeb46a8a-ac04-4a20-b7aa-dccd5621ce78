"""
Redis-based caching system for high-performance API endpoints
Provides ultra-fast caching with automatic invalidation strategies
"""

import redis
import json
import logging
import time
import os
from typing import Any, Optional, Union, Dict, List
from functools import wraps
from datetime import datetime, timedelta

class RedisCache:
    """High-performance Redis cache manager with automatic invalidation"""
    
    def __init__(self, redis_url: str = None, default_ttl: int = 300):
        """
        Initialize Redis cache
        
        Args:
            redis_url: Redis connection URL
            default_ttl: Default time-to-live in seconds (5 minutes)
        """
        self.default_ttl = default_ttl
        self.redis_url = redis_url or os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        
        try:
            # Initialize Redis connection with connection pooling
            self.redis_client = redis.from_url(
                self.redis_url,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30,
                max_connections=20
            )
            
            # Test connection
            self.redis_client.ping()
            logging.info("Redis cache initialized successfully")
            self.enabled = True
            
        except Exception as e:
            logging.warning(f"Redis cache initialization failed: {str(e)}. Falling back to in-memory cache.")
            self.redis_client = None
            self.enabled = False
            # Fallback to in-memory cache
            self._memory_cache = {}
    
    def _serialize_value(self, value: Any) -> str:
        """Serialize value for Redis storage"""
        try:
            return json.dumps(value, default=str, ensure_ascii=False)
        except Exception as e:
            logging.error(f"Error serializing value: {str(e)}")
            return json.dumps({"error": "serialization_failed"})
    
    def _deserialize_value(self, value: str) -> Any:
        """Deserialize value from Redis"""
        try:
            return json.loads(value)
        except Exception as e:
            logging.error(f"Error deserializing value: {str(e)}")
            return None
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get value from cache"""
        try:
            if self.enabled and self.redis_client:
                value = self.redis_client.get(key)
                if value is not None:
                    return self._deserialize_value(value)
            else:
                # Fallback to memory cache
                cache_item = self._memory_cache.get(key)
                if cache_item and cache_item['expires'] > time.time():
                    return cache_item['value']
                elif cache_item:
                    del self._memory_cache[key]
            
            return default
            
        except Exception as e:
            logging.error(f"Error getting cache key {key}: {str(e)}")
            return default
    
    def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """Set value in cache with TTL"""
        try:
            ttl = ttl or self.default_ttl
            
            if self.enabled and self.redis_client:
                serialized_value = self._serialize_value(value)
                return self.redis_client.setex(key, ttl, serialized_value)
            else:
                # Fallback to memory cache
                self._memory_cache[key] = {
                    'value': value,
                    'expires': time.time() + ttl
                }
                return True
                
        except Exception as e:
            logging.error(f"Error setting cache key {key}: {str(e)}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete key from cache"""
        try:
            if self.enabled and self.redis_client:
                return bool(self.redis_client.delete(key))
            else:
                # Fallback to memory cache
                if key in self._memory_cache:
                    del self._memory_cache[key]
                    return True
                return False
                
        except Exception as e:
            logging.error(f"Error deleting cache key {key}: {str(e)}")
            return False
    
    def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching pattern"""
        try:
            if self.enabled and self.redis_client:
                keys = self.redis_client.keys(pattern)
                if keys:
                    return self.redis_client.delete(*keys)
                return 0
            else:
                # Fallback to memory cache
                import fnmatch
                keys_to_delete = [key for key in self._memory_cache.keys() if fnmatch.fnmatch(key, pattern)]
                for key in keys_to_delete:
                    del self._memory_cache[key]
                return len(keys_to_delete)
                
        except Exception as e:
            logging.error(f"Error deleting cache pattern {pattern}: {str(e)}")
            return 0
    
    def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        try:
            if self.enabled and self.redis_client:
                return bool(self.redis_client.exists(key))
            else:
                cache_item = self._memory_cache.get(key)
                if cache_item and cache_item['expires'] > time.time():
                    return True
                elif cache_item:
                    del self._memory_cache[key]
                return False
                
        except Exception as e:
            logging.error(f"Error checking cache key existence {key}: {str(e)}")
            return False
    
    def increment(self, key: str, amount: int = 1) -> int:
        """Increment a counter in cache"""
        try:
            if self.enabled and self.redis_client:
                return self.redis_client.incr(key, amount)
            else:
                # Fallback to memory cache
                current = self.get(key, 0)
                new_value = current + amount
                self.set(key, new_value)
                return new_value
                
        except Exception as e:
            logging.error(f"Error incrementing cache key {key}: {str(e)}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        try:
            if self.enabled and self.redis_client:
                info = self.redis_client.info()
                return {
                    'enabled': True,
                    'connected_clients': info.get('connected_clients', 0),
                    'used_memory': info.get('used_memory_human', '0B'),
                    'keyspace_hits': info.get('keyspace_hits', 0),
                    'keyspace_misses': info.get('keyspace_misses', 0),
                    'hit_rate': info.get('keyspace_hits', 0) / max(info.get('keyspace_hits', 0) + info.get('keyspace_misses', 0), 1)
                }
            else:
                return {
                    'enabled': False,
                    'memory_cache_size': len(self._memory_cache),
                    'fallback_mode': True
                }
                
        except Exception as e:
            logging.error(f"Error getting cache stats: {str(e)}")
            return {'enabled': False, 'error': str(e)}


# Global cache instance
cache = RedisCache()


def cache_key(*args, **kwargs) -> str:
    """Generate cache key from arguments"""
    key_parts = []
    for arg in args:
        if hasattr(arg, 'id'):
            key_parts.append(str(arg.id))
        else:
            key_parts.append(str(arg))
    
    for k, v in sorted(kwargs.items()):
        key_parts.append(f"{k}:{v}")
    
    return ":".join(key_parts)


def cached(ttl: int = 300, key_prefix: str = ""):
    """
    Decorator for caching function results
    
    Args:
        ttl: Time to live in seconds
        key_prefix: Prefix for cache key
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            func_key = f"{key_prefix}:{func.__name__}" if key_prefix else func.__name__
            cache_key_str = cache_key(func_key, *args, **kwargs)
            
            # Try to get from cache
            cached_result = cache.get(cache_key_str)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key_str, result, ttl)
            
            return result
        return wrapper
    return decorator


def invalidate_cache_pattern(pattern: str):
    """Invalidate all cache keys matching pattern"""
    return cache.delete_pattern(pattern)


def invalidate_user_cache(user_id: str):
    """Invalidate all cache entries for a specific user"""
    patterns = [
        f"friends:*:{user_id}:*",
        f"friends:{user_id}:*",
        f"chat:*:{user_id}:*",
        f"messages:*:{user_id}:*"
    ]
    
    total_deleted = 0
    for pattern in patterns:
        total_deleted += cache.delete_pattern(pattern)
    
    return total_deleted


def invalidate_chat_cache(chat_id: str):
    """Invalidate all cache entries for a specific chat"""
    patterns = [
        f"chat:{chat_id}:*",
        f"messages:{chat_id}:*"
    ]
    
    total_deleted = 0
    for pattern in patterns:
        total_deleted += cache.delete_pattern(pattern)
    
    return total_deleted
