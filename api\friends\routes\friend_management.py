"""
Friend management routes.
This module contains routes for managing friend relationships.
"""
from flask import jsonify, request
from flask_login import login_required, current_user
from .. import friends_api
from models.user import User
from models.friend_relationship import FriendRelationship
from models.friend_chat import FriendChat
from utils.decorators import check_service_access
from utils.api_logger import log_api_request
from utils.redis_cache import cache, cached, invalidate_user_cache
from utils.performance_monitor import monitor_api_performance, track_cache_hit, track_cache_miss, track_db_query
import logging
import time


@friends_api.route('/friends', methods=['GET'])
@login_required
@check_service_access('friends')
@monitor_api_performance('get_friends')
def get_friends():
    """Get all friends for the current user with ultra-fast caching"""
    start_time = time.time()

    # Generate cache key for this user's friends list
    cache_key = f"friends:{current_user.id}:list"

    # Try to get from cache first (5-minute cache for ultra-fast performance)
    cached_result = cache.get(cache_key)
    if cached_result is not None:
        track_cache_hit()
        # Add performance metrics to cached response
        cached_result['cached'] = True
        cached_result['response_time_ms'] = round((time.time() - start_time) * 1000, 2)
        return jsonify(cached_result)

    # Cache miss - fetch from database with optimized queries
    track_cache_miss()
    friend_list = get_optimized_friends_list(current_user.id)

    # Prepare response with metadata
    response_data = {
        'friends': friend_list,
        'count': len(friend_list),
        'cached': False,
        'response_time_ms': round((time.time() - start_time) * 1000, 2)
    }

    # Cache the result for 5 minutes (300 seconds)
    cache.set(cache_key, response_data, ttl=300)

    return jsonify(response_data)


def get_optimized_friends_list(user_id):
    """
    Optimized friends list retrieval with minimal database queries
    """
    # Get all friend relationships in a single optimized query
    track_db_query()
    friends_as_user = list(FriendRelationship.objects(user=user_id, is_accepted=True).select_related())
    track_db_query()
    friends_as_friend = list(FriendRelationship.objects(friend=user_id, is_accepted=True).select_related())

    # Extract friend objects efficiently
    friends = []
    friend_ids = set()

    for relationship in friends_as_user:
        if relationship.friend.id not in friend_ids:
            friends.append(relationship.friend)
            friend_ids.add(relationship.friend.id)

    for relationship in friends_as_friend:
        if relationship.user.id not in friend_ids:
            friends.append(relationship.user)
            friend_ids.add(relationship.user.id)

    # Get all chats for this user in a single query to avoid N+1 problem
    user_chats = {}
    track_db_query()
    all_chats = FriendChat.objects(user1=user_id) | FriendChat.objects(user2=user_id)

    for chat in all_chats:
        other_user_id = str(chat.user2.id) if str(chat.user1.id) == str(user_id) else str(chat.user1.id)
        user_chats[other_user_id] = chat

    # Build optimized friend list
    friend_list = []
    for friend in friends:
        friend_id_str = str(friend.id)
        chat = user_chats.get(friend_id_str)

        # Get last message efficiently
        last_message = None
        if chat and chat.messages:
            last_message = chat.messages[-1]
            # Only include essential fields for last message to reduce payload
            if last_message:
                last_message = {
                    'content': last_message.get('content', ''),
                    'timestamp': last_message.get('timestamp', ''),
                    'username': last_message.get('username', ''),
                    'encrypted': last_message.get('encrypted', False),
                    'e2e_encrypted': last_message.get('e2e_encrypted', False)
                }

        friend_list.append({
            'id': friend_id_str,
            'username': friend.username,
            'display_name': friend.display_name or friend.username,
            'profile_picture': friend.profile_picture,
            'chat_id': chat.chat_id if chat else None,
            'last_message': last_message,
            'unread_count': 0  # TODO: Implement unread count efficiently
        })

    # Sort by last message timestamp for better UX
    friend_list.sort(key=lambda x: x['last_message']['timestamp'] if x['last_message'] else '', reverse=True)

    return friend_list


@friends_api.route('/request', methods=['POST'])
@login_required
@check_service_access('friends')
@log_api_request('friend_request', 'friends')
def send_friend_request():
    """Send a friend request"""
    data = request.json
    logging.info(f"Friend request data: {data}")

    friend_id = data.get('friend_id')
    logging.info(f"Friend ID: {friend_id}")

    if not friend_id:
        logging.error("Friend ID is required but not provided")
        return jsonify({'error': 'friend_id is required'}), 400

    # Check if friend exists
    try:
        friend = User.objects(id=friend_id).first()
        logging.info(f"Friend found: {friend.username if friend else 'None'}")
    except Exception as e:
        logging.error(f"Error finding friend: {str(e)}")
        return jsonify({'error': f'Invalid friend ID format: {str(e)}'}), 400

    if not friend:
        logging.error(f"User with ID {friend_id} not found")
        return jsonify({'error': 'User not found'}), 404

    # Check if user already has 7 friends
    current_friends = FriendRelationship.get_friends(current_user.id)
    if len(current_friends) >= 7:
        logging.error(f"User {current_user.username} has reached the maximum number of friends (7)")
        return jsonify({'error': 'You have reached the maximum number of friends (7). Please remove a friend before adding a new one.'}), 400

    # Send request
    try:
        success, message = FriendRelationship.send_request(current_user.id, friend.id)
        logging.info(f"Send request result: success={success}, message={message}")
    except Exception as e:
        logging.error(f"Error sending friend request: {str(e)}")
        return jsonify({'error': f'Error sending friend request: {str(e)}'}), 500

    if not success:
        return jsonify({'error': message}), 400

    return jsonify({'success': True, 'message': message})


@friends_api.route('/accept', methods=['POST'])
@login_required
@check_service_access('friends')
@log_api_request('friend_accept', 'friends')
def accept_friend_request():
    """Accept a friend request"""
    data = request.json
    friend_id = data.get('friend_id')

    if not friend_id:
        return jsonify({'error': 'friend_id is required'}), 400

    # Check if friend exists
    friend = User.objects(id=friend_id).first()
    if not friend:
        return jsonify({'error': 'User not found'}), 404

    # Check if user already has 7 friends
    current_friends = FriendRelationship.get_friends(current_user.id)
    if len(current_friends) >= 7:
        logging.error(f"User {current_user.username} has reached the maximum number of friends (7)")
        return jsonify({'error': 'You have reached the maximum number of friends (7). Please remove a friend before accepting a new one.'}), 400

    # Accept request
    success, message = FriendRelationship.accept_request(current_user.id, friend.id)

    if not success:
        return jsonify({'error': message}), 400

    # Create a chat for the new friendship
    chat = FriendChat.get_or_create_chat(current_user.id, friend.id)

    # Invalidate cache for both users when friendship is accepted
    invalidate_user_cache(str(current_user.id))
    invalidate_user_cache(str(friend.id))

    return jsonify({
        'success': True,
        'message': message,
        'chat_id': chat.chat_id
    })


@friends_api.route('/request/accept', methods=['POST'])
@login_required
@check_service_access('friends')
def accept_friend_request_alias():
    """Alias for accept_friend_request"""
    return accept_friend_request()


@friends_api.route('/reject', methods=['POST'])
@login_required
@check_service_access('friends')
@log_api_request('friend_reject', 'friends')
def reject_friend_request():
    """Reject a friend request"""
    data = request.json
    friend_id = data.get('friend_id')

    if not friend_id:
        return jsonify({'error': 'friend_id is required'}), 400

    # Check if friend exists
    friend = User.objects(id=friend_id).first()
    if not friend:
        return jsonify({'error': 'User not found'}), 404

    # Delete the request
    FriendRelationship.objects(user=friend.id, friend=current_user.id, is_accepted=False).delete()

    return jsonify({'success': True, 'message': 'Friend request rejected'})


@friends_api.route('/cancel', methods=['POST'])
@login_required
@check_service_access('friends')
@log_api_request('friend_cancel', 'friends')
def cancel_friend_request():
    """Cancel a sent friend request"""
    data = request.json
    friend_id = data.get('friend_id')

    if not friend_id:
        return jsonify({'error': 'friend_id is required'}), 400

    # Check if friend exists
    friend = User.objects(id=friend_id).first()
    if not friend:
        return jsonify({'error': 'User not found'}), 404

    # Delete the request sent by the current user
    deleted = FriendRelationship.objects(user=current_user.id, friend=friend.id, is_accepted=False).delete()

    if deleted == 0:
        # No request was found to delete
        logging.warning(f"No friend request found from {current_user.username} to {friend.username}")
        return jsonify({'warning': 'No friend request found to cancel'}), 200

    logging.info(f"Canceled friend request from {current_user.username} to {friend.username}")
    return jsonify({'success': True, 'message': 'Friend request canceled'})


@friends_api.route('/remove', methods=['POST'])
@login_required
@check_service_access('friends')
@log_api_request('friend_remove', 'friends')
def remove_friend():
    """Remove a friend"""
    data = request.json
    friend_id = data.get('friend_id')

    if not friend_id:
        return jsonify({'error': 'friend_id is required'}), 400

    # Check if friend exists
    friend = User.objects(id=friend_id).first()
    if not friend:
        return jsonify({'error': 'User not found'}), 404

    # Check if they are friends
    if not FriendRelationship.are_friends(current_user.id, friend.id):
        return jsonify({'error': 'Not friends with this user'}), 400

    # Remove friendship in both directions
    FriendRelationship.objects(user=current_user.id, friend=friend.id).delete()
    FriendRelationship.objects(user=friend.id, friend=current_user.id).delete()

    # Invalidate cache for both users when friendship is removed
    invalidate_user_cache(str(current_user.id))
    invalidate_user_cache(str(friend.id))

    return jsonify({'success': True, 'message': 'Friend removed'})


@friends_api.route('/pending', methods=['GET'])
@login_required
@check_service_access('friends')
def get_pending_requests():
    """Get pending friend requests"""
    # Get requests sent to the current user
    pending_requests = FriendRelationship.objects(friend=current_user.id, is_accepted=False)

    # Convert to list of dictionaries
    request_list = []
    for request in pending_requests:
        request_list.append({
            'id': str(request.user.id),
            'user_id': str(request.user.id),  # Add user_id field for client compatibility
            'username': request.user.username,
            'display_name': request.user.display_name or request.user.username,
            'profile_picture': request.user.profile_picture,
            'created_at': request.created_at.isoformat()
        })

    return jsonify(request_list)


@friends_api.route('/requests/sent', methods=['GET'])
@login_required
@check_service_access('friends')
def get_sent_requests():
    """Get friend requests sent by the current user"""
    # Get requests sent by the current user
    sent_requests = FriendRelationship.objects(user=current_user.id, is_accepted=False)

    # Convert to list of dictionaries
    request_list = []
    for request in sent_requests:
        request_list.append({
            'id': str(request.friend.id),
            'user_id': str(request.friend.id),  # Add user_id field for client compatibility
            'username': request.friend.username,
            'display_name': request.friend.display_name or request.friend.username,
            'profile_picture': request.friend.profile_picture,
            'created_at': request.created_at.isoformat()
        })

    return jsonify(request_list)


@friends_api.route('/request/sent', methods=['GET'])
@login_required
@check_service_access('friends')
def get_sent_requests_alias():
    """Alias for get_sent_requests"""
    return get_sent_requests()


@friends_api.route('/invite-to-live', methods=['POST'])
@login_required
@check_service_access('friends')
@log_api_request('friend_invite_live', 'friends')
def invite_to_live():
    """Invite a friend to a live chat"""
    data = request.json
    friend_id = data.get('friend_id')
    room_id = data.get('room_id')

    if not friend_id or not room_id:
        return jsonify({'error': 'friend_id and room_id are required'}), 400

    # Check if friend exists
    friend = User.objects(id=friend_id).first()
    if not friend:
        return jsonify({'error': 'User not found'}), 404

    # Check if they are friends
    if not FriendRelationship.are_friends(current_user.id, friend.id):
        return jsonify({'error': 'Not friends with this user'}), 400

    # The actual invitation will be handled by the socket.io connection
    return jsonify({'success': True, 'message': 'Invitation sent'})