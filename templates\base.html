
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <!-- Add cache prevention meta tags -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    
    <style>
        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow-x: hidden;
        }
    </style>

    <!-- Favicon links -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='favicon-16x16.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="96x96" href="{{ url_for('static', filename='favicon-96x96.png') }}">
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <title>{% block title %}Kevko Systems{% endblock %}</title>

    <!-- Resource hints -->
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    <link rel="preload" href="{{ url_for('static', filename='css/base.css') }}" as="style">

    <!-- Optimized CSS loading -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/base.css') }}" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css" media="print" onload="this.media='all'">
    <noscript>
        <link rel="stylesheet" href="{{ url_for('static', filename='css/base.css') }}">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css">
    </noscript>
    {% block extra_css %}{% endblock %}
</head>
<body{% if current_user.is_authenticated %} data-authenticated="true"{% endif %}>
    <!-- Main Content -->
    <main style="width: 100%; min-height: 100vh; margin: 0; padding: 0;">
        {% block content %}{% endblock %}
    </main>

    <!-- Common Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="{{ url_for('static', filename='js/ProfileCustomization.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>


