"""
<PERSON><PERSON><PERSON> to optimize MongoDB performance for the friends feature.
This script creates indexes and optimizations to improve query performance.
"""

import sys
import os
import logging
from datetime import datetime

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from models.friend_chat import FriendChat
from models.friend_relationship import FriendRelationship
from models.user import User

def create_optimized_indexes():
    """Create optimized indexes for friends feature"""
    try:
        print("Creating optimized indexes for Friends feature...")
        
        # FriendRelationship indexes
        print("Creating FriendRelationship indexes...")
        FriendRelationship.ensure_indexes()
        
        # Create compound indexes for efficient friend lookups
        FriendRelationship.objects._collection.create_index([
            ("user", 1), ("is_accepted", 1)
        ], name="user_accepted_idx")
        
        FriendRelationship.objects._collection.create_index([
            ("friend", 1), ("is_accepted", 1)
        ], name="friend_accepted_idx")
        
        # Compound index for bidirectional friend checks
        FriendRelationship.objects._collection.create_index([
            ("user", 1), ("friend", 1), ("is_accepted", 1)
        ], name="user_friend_accepted_idx")
        
        # Index for pending requests
        FriendRelationship.objects._collection.create_index([
            ("friend", 1), ("is_accepted", 1), ("created_at", -1)
        ], name="pending_requests_idx")
        
        # FriendChat indexes
        print("Creating FriendChat indexes...")
        FriendChat.ensure_indexes()
        
        # Create compound indexes for efficient chat lookups
        FriendChat.objects._collection.create_index([
            ("user1", 1), ("user2", 1)
        ], name="user1_user2_idx", unique=True)
        
        FriendChat.objects._collection.create_index([
            ("user2", 1), ("user1", 1)
        ], name="user2_user1_idx")
        
        # Index for chat updates (for sorting by last activity)
        FriendChat.objects._collection.create_index([
            ("updated_at", -1)
        ], name="updated_at_desc_idx")
        
        # Compound index for user chats sorted by activity
        FriendChat.objects._collection.create_index([
            ("user1", 1), ("updated_at", -1)
        ], name="user1_updated_idx")
        
        FriendChat.objects._collection.create_index([
            ("user2", 1), ("updated_at", -1)
        ], name="user2_updated_idx")
        
        # Index for message count optimization
        FriendChat.objects._collection.create_index([
            ("chat_id", 1), ("updated_at", -1)
        ], name="chat_id_updated_idx")
        
        # User indexes for friend lookups
        print("Creating User indexes for friend optimization...")
        User.ensure_indexes()
        
        # Index for user lookups by ID (should already exist but ensure it's optimized)
        User.objects._collection.create_index([("_id", 1)], name="id_idx")
        
        # Index for username lookups
        User.objects._collection.create_index([("username", 1)], name="username_idx")
        
        print("Indexes created successfully!")
        
        # Verify indexes
        print("\nVerifying FriendRelationship indexes:")
        fr_indexes = FriendRelationship.objects._collection.index_information()
        for name, info in fr_indexes.items():
            print(f"  - {name}: {info}")
        
        print("\nVerifying FriendChat indexes:")
        fc_indexes = FriendChat.objects._collection.index_information()
        for name, info in fc_indexes.items():
            print(f"  - {name}: {info}")
        
        print("\nVerifying User indexes:")
        user_indexes = User.objects._collection.index_information()
        for name, info in user_indexes.items():
            print(f"  - {name}: {info}")
        
    except Exception as e:
        print(f"Error creating indexes: {str(e)}")
        import traceback
        traceback.print_exc()

def analyze_query_performance():
    """Analyze current query performance"""
    try:
        print("\nAnalyzing query performance...")
        
        # Get collection stats
        fr_stats = FriendRelationship.objects._collection.stats()
        fc_stats = FriendChat.objects._collection.stats()
        
        print(f"FriendRelationship collection stats:")
        print(f"  - Document count: {fr_stats.get('count', 0)}")
        print(f"  - Average document size: {fr_stats.get('avgObjSize', 0)} bytes")
        print(f"  - Total size: {fr_stats.get('size', 0)} bytes")
        
        print(f"FriendChat collection stats:")
        print(f"  - Document count: {fc_stats.get('count', 0)}")
        print(f"  - Average document size: {fc_stats.get('avgObjSize', 0)} bytes")
        print(f"  - Total size: {fc_stats.get('size', 0)} bytes")
        
    except Exception as e:
        print(f"Error analyzing performance: {str(e)}")

def optimize_existing_data():
    """Optimize existing data for better performance"""
    try:
        print("\nOptimizing existing data...")
        
        # Update all chats to ensure updated_at is set
        chats_updated = 0
        for chat in FriendChat.objects(updated_at__exists=False):
            chat.updated_at = chat.created_at or datetime.utcnow()
            chat.save()
            chats_updated += 1
        
        print(f"Updated {chats_updated} chats with missing updated_at timestamps")
        
        # Ensure all relationships have created_at
        relationships_updated = 0
        for relationship in FriendRelationship.objects(created_at__exists=False):
            relationship.created_at = datetime.utcnow()
            relationship.save()
            relationships_updated += 1
        
        print(f"Updated {relationships_updated} relationships with missing created_at timestamps")
        
    except Exception as e:
        print(f"Error optimizing data: {str(e)}")

def main():
    """Main optimization function"""
    print("=== Friends Feature Performance Optimization ===")
    print(f"Started at: {datetime.now()}")
    
    # Create optimized indexes
    create_optimized_indexes()
    
    # Analyze performance
    analyze_query_performance()
    
    # Optimize existing data
    optimize_existing_data()
    
    print(f"\nOptimization completed at: {datetime.now()}")
    print("=== Optimization Complete ===")

if __name__ == "__main__":
    main()
