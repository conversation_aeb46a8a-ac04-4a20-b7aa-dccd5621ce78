/**
 * AdminCredits.js
 * Handles admin credit management functionality
 */
class AdminCredits {
    constructor() {
        this.users = [];
        this.modelCosts = [];
        this.isLoading = false;
    }

    /**
     * Initialize the admin credits section
     */
    init() {
        console.log('Initializing Admin Credits...');

        // Fetch initial data
        this.fetchUserCredits();
        this.fetchModelCosts();

        // Set up event listeners
        this.setupEventListeners();
    }

    /**
     * Set up event listeners for the admin credits section
     */
    setupEventListeners() {
        // Refresh user credits button
        const refreshUserCreditsBtn = document.getElementById('refreshUserCreditsBtn');
        if (refreshUserCreditsBtn) {
            refreshUserCreditsBtn.addEventListener('click', () => {
                this.fetchUserCredits();
            });
        }

        // Refresh model costs button
        const refreshModelCostsBtn = document.getElementById('refreshModelCostsBtn');
        if (refreshModelCostsBtn) {
            refreshModelCostsBtn.addEventListener('click', () => {
                this.fetchModelCosts();
            });
        }

        // Initialize default costs button
        const initializeDefaultCostsBtn = document.getElementById('initializeDefaultCostsBtn');
        if (initializeDefaultCostsBtn) {
            initializeDefaultCostsBtn.addEventListener('click', () => {
                this.initializeDefaultCosts();
            });
        }

        // Add credits button
        const addCreditsBtn = document.getElementById('addCreditsBtn');
        if (addCreditsBtn) {
            addCreditsBtn.addEventListener('click', () => {
                this.showAddCreditsModal();
            });
        }

        // Update model cost button
        const updateModelCostBtn = document.getElementById('updateModelCostBtn');
        if (updateModelCostBtn) {
            updateModelCostBtn.addEventListener('click', () => {
                this.showUpdateModelCostModal();
            });
        }
    }

    /**
     * Fetch user credits data from the API
     */
    async fetchUserCredits() {
        try {
            this.setLoading(true, 'users');

            const response = await fetch('/api/admin/credits/users');
            if (!response.ok) {
                throw new Error('Failed to fetch user credits');
            }

            const data = await response.json();
            this.users = data;

            // Update UI
            this.updateUserCreditsTable();

            this.setLoading(false, 'users');
        } catch (error) {
            console.error('Error fetching user credits:', error);
            this.setLoading(false, 'users');

            // Show error message
            const userCreditsTable = document.getElementById('userCreditsTable');
            if (userCreditsTable) {
                userCreditsTable.innerHTML = `
                    <div class="text-center py-4 text-red-400">
                        <p>Error loading user credits. Please try again.</p>
                    </div>
                `;
            }
        }
    }

    /**
     * Fetch model costs data from the API
     */
    async fetchModelCosts() {
        try {
            this.setLoading(true, 'models');

            const response = await fetch('/api/admin/credits/model-costs');
            if (!response.ok) {
                throw new Error('Failed to fetch model costs');
            }

            const data = await response.json();
            this.modelCosts = data;

            // Update UI
            this.updateModelCostsTable();

            this.setLoading(false, 'models');
        } catch (error) {
            console.error('Error fetching model costs:', error);
            this.setLoading(false, 'models');

            // Show error message
            const modelCostsTable = document.getElementById('modelCostsTable');
            if (modelCostsTable) {
                modelCostsTable.innerHTML = `
                    <div class="text-center py-4 text-red-400">
                        <p>Error loading model costs. Please try again.</p>
                    </div>
                `;
            }
        }
    }

    /**
     * Initialize default model costs
     */
    async initializeDefaultCosts() {
        try {
            this.setLoading(true, 'models');

            const response = await fetch('/api/admin/credits/initialize-defaults', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to initialize default costs');
            }

            const data = await response.json();

            if (data.success) {
                // Show success message
                this.showNotification('Default model costs initialized successfully', 'success');

                // Update model costs
                this.modelCosts = data.model_costs;
                this.updateModelCostsTable();
            } else {
                throw new Error(data.error || 'Failed to initialize default costs');
            }

            this.setLoading(false, 'models');
        } catch (error) {
            console.error('Error initializing default costs:', error);
            this.setLoading(false, 'models');
            this.showNotification(error.message, 'error');
        }
    }

    /**
     * Update a user's credit balance
     * @param {string} userId User ID
     * @param {number} amount Amount to add (positive) or subtract (negative)
     * @param {string} reason Reason for the adjustment
     */
    async updateUserCredits(userId, amount, reason) {
        try {
            this.setLoading(true, 'users');

            const response = await fetch('/api/admin/credits/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: userId,
                    amount: amount,
                    reason: reason
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to update user credits');
            }

            const data = await response.json();

            if (data.success) {
                // Show appropriate success message based on whether adding or removing credits
                let message;
                if (amount > 0) {
                    message = `Added ${amount} credits to ${data.username}`;
                } else {
                    message = `Removed ${Math.abs(amount)} credits from ${data.username}`;
                }
                this.showNotification(message, 'success');

                // Refresh user credits
                this.fetchUserCredits();
            } else {
                throw new Error(data.error || 'Failed to update user credits');
            }

            this.setLoading(false, 'users');
        } catch (error) {
            console.error('Error updating user credits:', error);
            this.setLoading(false, 'users');
            this.showNotification(error.message, 'error');
        }
    }

    /**
     * Update a model's credit cost
     * @param {string} modelName Model name
     * @param {number} creditCost New credit cost
     */
    async updateModelCost(modelName, creditCost) {
        try {
            this.setLoading(true, 'models');

            const response = await fetch('/api/admin/credits/model-cost', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model_name: modelName,
                    credit_cost: creditCost
                })
            });

            if (!response.ok) {
                throw new Error('Failed to update model cost');
            }

            const data = await response.json();

            if (data.success) {
                // Show success message
                this.showNotification(`Updated ${modelName} cost to ${creditCost} credits`, 'success');

                // Refresh model costs
                this.fetchModelCosts();
            } else {
                throw new Error(data.error || 'Failed to update model cost');
            }

            this.setLoading(false, 'models');
        } catch (error) {
            console.error('Error updating model cost:', error);
            this.setLoading(false, 'models');
            this.showNotification(error.message, 'error');
        }
    }
    
    /**
     * Delete a model's credit cost
     * @param {string} modelName Model name
     */
    async deleteModelCost(modelName) {
        try {
            this.setLoading(true, 'models');

            const response = await fetch('/api/admin/credits/delete-model-cost', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model_name: modelName
                })
            });

            if (!response.ok) {
                throw new Error('Failed to delete model cost');
            }

            const data = await response.json();

            if (data.success) {
                // Show success message
                this.showNotification(`Deleted cost for ${modelName}`, 'success');

                // Refresh model costs
                this.fetchModelCosts();
            } else {
                throw new Error(data.error || 'Failed to delete model cost');
            }

            this.setLoading(false, 'models');
        } catch (error) {
            console.error('Error deleting model cost:', error);
            this.setLoading(false, 'models');
            this.showNotification(error.message, 'error');
        }
    }

    /**
     * Update the user credits table with the latest data
     */
    updateUserCreditsTable() {
        const userCreditsTable = document.getElementById('userCreditsTable');
        if (!userCreditsTable) return;

        if (this.users.length === 0) {
            userCreditsTable.innerHTML = `
                <div class="text-center py-4 text-slate-400">
                    <p>No user credits found</p>
                </div>
            `;
            return;
        }

        // Create table HTML
        let tableHTML = `
            <table class="min-w-full divide-y divide-slate-700">
                <thead>
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">User</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Balance</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Total Earned</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Last Updated</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-slate-400 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-slate-700">
        `;

        // Add rows for each user
        this.users.forEach(user => {
            tableHTML += `
                <tr>
                    <td class="px-4 py-3 whitespace-nowrap">
                        <div class="flex items-center">
                            <div>
                                <div class="text-sm font-medium text-slate-200">${user.username}</div>
                                <div class="text-xs text-slate-400">${user.email}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap">
                        <div class="text-sm text-slate-300">${user.balance}</div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap">
                        <div class="text-sm text-slate-300">${user.total_earned}</div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap">
                        <div class="text-sm text-slate-300">${this.formatDate(new Date(user.last_updated))}</div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-right">
                        <button class="text-xs bg-cyan-900/30 hover:bg-cyan-900/50 text-cyan-400 px-2 py-1 rounded flex items-center ml-auto add-credits-btn" data-user-id="${user.user_id}" data-username="${user.username}">
                            <i data-lucide="edit" class="h-3 w-3 mr-1"></i>
                            Manage Credits
                        </button>
                    </td>
                </tr>
            `;
        });

        tableHTML += `
                </tbody>
            </table>
        `;

        userCreditsTable.innerHTML = tableHTML;

        // Initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons({
                attrs: {
                    class: ["h-3", "w-3"]
                },
                elements: [userCreditsTable]
            });
        }

        // Add event listeners to add credits buttons
        document.querySelectorAll('.add-credits-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const userId = e.currentTarget.getAttribute('data-user-id');
                const username = e.currentTarget.getAttribute('data-username');
                this.showAddCreditsModal(userId, username);
            });
        });
    }

    /**
     * Update the model costs table with the latest data
     */
    updateModelCostsTable() {
        const modelCostsTable = document.getElementById('modelCostsTable');
        if (!modelCostsTable) return;

        if (this.modelCosts.length === 0) {
            modelCostsTable.innerHTML = `
                <div class="text-center py-4 text-slate-400">
                    <p>No model costs found</p>
                </div>
            `;
            return;
        }

        // Create table HTML
        let tableHTML = `
            <table class="min-w-full divide-y divide-slate-700">
                <thead>
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Model</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Credit Cost</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-slate-400 uppercase tracking-wider">Last Updated</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-slate-400 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-slate-700">
        `;

        // Add rows for each model
        this.modelCosts.forEach(model => {
            tableHTML += `
                <tr>
                    <td class="px-4 py-3 whitespace-nowrap">
                        <div class="text-sm font-medium text-slate-200">${model.model_name}</div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap">
                        <div class="text-sm text-slate-300">${model.credit_cost}</div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap">
                        <div class="text-sm text-slate-300">${this.formatDate(new Date(model.last_updated))}</div>
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-right">
                        <div class="flex space-x-2 justify-end">
                            <button class="text-xs bg-cyan-900/30 hover:bg-cyan-900/50 text-cyan-400 px-2 py-1 rounded flex items-center edit-model-cost-btn" data-model-name="${model.model_name}" data-cost="${model.credit_cost}">
                                <i data-lucide="edit" class="h-3 w-3 mr-1"></i>
                                Edit
                            </button>
                            <button class="text-xs bg-red-900/30 hover:bg-red-900/50 text-red-400 px-2 py-1 rounded flex items-center delete-model-cost-btn" data-model-name="${model.model_name}">
                                <i data-lucide="trash-2" class="h-3 w-3 mr-1"></i>
                                Delete
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        tableHTML += `
                </tbody>
            </table>
        `;

        modelCostsTable.innerHTML = tableHTML;

        // Initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons({
                attrs: {
                    class: ["h-3", "w-3"]
                },
                elements: [modelCostsTable]
            });
        }

        // Add event listeners to edit model cost buttons
        document.querySelectorAll('.edit-model-cost-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modelName = e.currentTarget.getAttribute('data-model-name');
                const cost = e.currentTarget.getAttribute('data-cost');
                this.showUpdateModelCostModal(modelName, cost);
            });
        });
        
        // Add event listeners to delete model cost buttons
        document.querySelectorAll('.delete-model-cost-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modelName = e.currentTarget.getAttribute('data-model-name');
                if (confirm(`Are you sure you want to delete the cost for ${modelName}?`)) {
                    this.deleteModelCost(modelName);
                }
            });
        });
    }

    /**
     * Format a date for display
     * @param {Date} date Date to format
     * @returns {string} Formatted date string
     */
    formatDate(date) {
        if (!date) return '';
        return date.toLocaleString();
    }

    /**
     * Set loading state
     * @param {boolean} isLoading Whether the component is loading
     * @param {string} section Section that is loading ('users' or 'models')
     */
    setLoading(isLoading, section) {
        this.isLoading = isLoading;

        // Update loading UI for the specified section
        const loadingIndicator = document.getElementById(`${section}LoadingIndicator`);
        if (loadingIndicator) {
            if (isLoading) {
                loadingIndicator.classList.remove('hidden');
            } else {
                loadingIndicator.classList.add('hidden');
            }
        }
    }

    /**
     * Show the add credits modal
     * @param {string} userId User ID to add credits to (optional)
     * @param {string} username Username to display (optional)
     */
    /**
     * Helper method to close modal properly
     * @param {HTMLElement} modal The modal element to close
     */
    closeModal(modal) {
        if (!modal) return;
        modal.classList.add('hidden');
        modal.style.display = 'none';
        document.body.style.overflow = ''; // Restore scrolling
    }

    showAddCreditsModal(userId = null, username = null) {
        try {
            // Get modal element
            const modal = document.getElementById('addCreditsModal');
            if (!modal) return;

            // Show modal
            modal.classList.remove('hidden');
            modal.style.display = 'flex';
            
            // Ensure modal is centered in viewport
            document.body.style.overflow = 'hidden'; // Prevent scrolling when modal is open

            // Set user if provided
            const userSelect = document.getElementById('creditUser');
            if (userSelect && userId) {
                // Create option if it doesn't exist
                let option = userSelect.querySelector(`option[value="${userId}"]`);
                if (!option) {
                    option = document.createElement('option');
                    option.value = userId;
                    option.textContent = username || userId;
                    userSelect.appendChild(option);
                }

                // Select the option
                userSelect.value = userId;
            } else if (userSelect) {
                // Populate user select with all users
                userSelect.innerHTML = '<option value="">Select a user</option>';

                this.users.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.user_id;
                    option.textContent = user.username || user.email;
                    userSelect.appendChild(option);
                });
            }

            // Set up event listeners
            const closeBtn = document.getElementById('closeAddCreditsModal');
            const cancelBtn = document.getElementById('cancelAddCredits');
            const confirmBtn = document.getElementById('confirmAddCredits');

            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    this.closeModal(modal);
                });
            }

            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => {
                    this.closeModal(modal);
                });
            }

            if (confirmBtn) {
                // Remove existing event listeners
                const newConfirmBtn = confirmBtn.cloneNode(true);
                confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

                // Add new event listener
                newConfirmBtn.addEventListener('click', () => {
                    const selectedUserId = document.getElementById('creditUser').value;
                    const amount = parseInt(document.getElementById('creditAmount').value);
                    const reason = document.getElementById('creditReason').value || 'Admin adjustment';

                    if (!selectedUserId) {
                        this.showNotification('Please select a user', 'error');
                        return;
                    }

                    if (isNaN(amount) || amount === 0) {
                        this.showNotification('Please enter a non-zero amount (positive to add, negative to remove)', 'error');
                        return;
                    }

                    // Update user credits
                    this.updateUserCredits(selectedUserId, amount, reason);

                    // Hide modal
                    this.closeModal(modal);
                });
            }
        } catch (error) {
            console.error('Error showing add credits modal:', error);
        }
    }

    /**
     * Show the update model cost modal
     * @param {string} modelName Model name to update (optional)
     * @param {number} currentCost Current cost of the model (optional)
     */
    showUpdateModelCostModal(modelName = null, currentCost = null) {
        try {
            // Get modal element
            const modal = document.getElementById('updateModelCostModal');
            if (!modal) return;

            // Show modal
            modal.classList.remove('hidden');

            // Set model if provided
            const modelSelect = document.getElementById('modelName');
            const costInput = document.getElementById('modelCost');

            if (modelSelect && modelName) {
                // Create option if it doesn't exist
                let option = modelSelect.querySelector(`option[value="${modelName}"]`);
                if (!option) {
                    option = document.createElement('option');
                    option.value = modelName;
                    option.textContent = modelName;
                    modelSelect.appendChild(option);
                }

                // Select the option
                modelSelect.value = modelName;
            } else if (modelSelect) {
                // Populate model select with all models
                modelSelect.innerHTML = '<option value="">Select a model</option>';

                this.modelCosts.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.model_name;
                    option.textContent = model.model_name;
                    modelSelect.appendChild(option);
                });
            }

            // Set current cost if provided
            if (costInput && currentCost !== null) {
                costInput.value = currentCost;
            }

            // Set up event listeners
            const closeBtn = document.getElementById('closeUpdateModelCostModal');
            const cancelBtn = document.getElementById('cancelUpdateModelCost');
            const confirmBtn = document.getElementById('confirmUpdateModelCost');

            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    modal.classList.add('hidden');
                });
            }

            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => {
                    modal.classList.add('hidden');
                });
            }

            if (confirmBtn) {
                // Remove existing event listeners
                const newConfirmBtn = confirmBtn.cloneNode(true);
                confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

                // Add new event listener
                newConfirmBtn.addEventListener('click', () => {
                    const selectedModelName = document.getElementById('modelName').value;
                    const cost = parseInt(document.getElementById('modelCost').value);

                    if (!selectedModelName) {
                        this.showNotification('Please select a model', 'error');
                        return;
                    }

                    if (isNaN(cost) || cost < 0) {
                        this.showNotification('Please enter a valid cost', 'error');
                        return;
                    }

                    // Update model cost
                    this.updateModelCost(selectedModelName, cost);

                    // Hide modal
                    modal.classList.add('hidden');
                });
            }
        } catch (error) {
            console.error('Error showing update model cost modal:', error);
        }
    }

    /**
     * Show a notification message
     * @param {string} message Message to show
     * @param {string} type Type of notification ('success', 'error', 'info')
     */
    showNotification(message, type = 'info') {
        // Implementation depends on the notification system used in the app
        console.log(`[${type}] ${message}`);

        // If a notification system is available, use it
        if (window.showNotification) {
            window.showNotification(message, type);
        }
    }
}
