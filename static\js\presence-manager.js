/**
 * PresenceManager.js
 * Manages real-time user presence monitoring via WebSocket connections
 */

class PresenceManager {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // Start with 1 second
        this.maxReconnectDelay = 30000; // Max 30 seconds
        this.heartbeatInterval = null;
        this.heartbeatFrequency = 30000; // 30 seconds
        this.statusCallbacks = new Map(); // Callbacks for status updates
        this.friendsStatusCallbacks = new Set(); // Callbacks for friends status updates
        this.debug = false;
        
        // Bind methods to preserve context
        this.connect = this.connect.bind(this);
        this.disconnect = this.disconnect.bind(this);
        this.handleConnect = this.handleConnect.bind(this);
        this.handleDisconnect = this.handleDisconnect.bind(this);
        this.handleReconnect = this.handleReconnect.bind(this);
        this.sendHeartbeat = this.sendHeartbeat.bind(this);
    }
    
    /**
     * Initialize the presence manager
     */
    init(options = {}) {
        this.debug = options.debug || false;
        
        this.log('Initializing PresenceManager');
        
        // Connect to the presence WebSocket
        this.connect();
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // Page became visible, ensure we're connected
                if (!this.isConnected) {
                    this.log('Page became visible, reconnecting...');
                    this.connect();
                }
            }
        });
        
        // Handle page unload
        window.addEventListener('beforeunload', () => {
            this.disconnect();
        });
        
        this.log('PresenceManager initialized');
    }
    
    /**
     * Connect to the presence WebSocket
     */
    connect() {
        if (this.socket && this.isConnected) {
            this.log('Already connected to presence WebSocket');
            return;
        }

        // Check if Socket.IO is available
        if (typeof io === 'undefined') {
            this.error('Socket.IO library not available. Please ensure Socket.IO is loaded before initializing PresenceManager.');
            return;
        }

        try {
            this.log('Connecting to presence WebSocket...');

            // Create socket connection to presence namespace
            this.socket = io('/presence', {
                transports: ['websocket', 'polling'],
                reconnection: false, // We'll handle reconnection manually
                timeout: 10000,
                autoConnect: true
            });

            // Set up event handlers
            this.setupEventHandlers();

        } catch (error) {
            this.error('Error creating WebSocket connection:', error);
            this.scheduleReconnect();
        }
    }
    
    /**
     * Set up WebSocket event handlers
     */
    setupEventHandlers() {
        if (!this.socket) return;
        
        this.socket.on('connect', this.handleConnect);
        this.socket.on('disconnect', this.handleDisconnect);
        this.socket.on('reconnect', this.handleReconnect);
        
        // Presence-specific events
        this.socket.on('presence_connected', (data) => {
            this.log('Presence connection confirmed:', data);
        });
        
        this.socket.on('presence_error', (data) => {
            this.error('Presence error:', data);
        });
        
        this.socket.on('heartbeat_ack', (data) => {
            this.log('Heartbeat acknowledged:', data);
        });
        
        this.socket.on('friend_status_change', (data) => {
            this.log('Friend status change:', data);
            this.handleFriendStatusChange(data);
        });
        
        this.socket.on('friends_status_update', (data) => {
            this.log('Friends status update:', data);
            this.handleFriendsStatusUpdate(data);
        });
    }
    
    /**
     * Handle successful connection
     */
    handleConnect() {
        this.log('Connected to presence WebSocket');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000; // Reset delay
        
        // Start heartbeat
        this.startHeartbeat();
        
        // Request initial friends status
        this.requestFriendsStatus();
    }
    
    /**
     * Handle disconnection
     */
    handleDisconnect(reason) {
        this.log('Disconnected from presence WebSocket:', reason);
        this.isConnected = false;
        
        // Stop heartbeat
        this.stopHeartbeat();
        
        // Schedule reconnection if not intentional
        if (reason !== 'io client disconnect') {
            this.scheduleReconnect();
        }
    }
    
    /**
     * Handle reconnection
     */
    handleReconnect() {
        this.log('Reconnected to presence WebSocket');
        this.handleConnect();
    }
    
    /**
     * Schedule a reconnection attempt
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            this.error('Max reconnection attempts reached');
            return;
        }
        
        this.reconnectAttempts++;
        const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);
        
        this.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
        
        setTimeout(() => {
            this.log(`Reconnection attempt ${this.reconnectAttempts}`);
            this.connect();
        }, delay);
    }
    
    /**
     * Start sending heartbeat signals
     */
    startHeartbeat() {
        this.stopHeartbeat(); // Clear any existing interval
        
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected && this.socket) {
                this.sendHeartbeat();
            }
        }, this.heartbeatFrequency);
        
        this.log('Heartbeat started');
    }
    
    /**
     * Stop sending heartbeat signals
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
            this.log('Heartbeat stopped');
        }
    }
    
    /**
     * Send a heartbeat signal
     */
    sendHeartbeat() {
        if (this.socket && this.isConnected) {
            this.socket.emit('heartbeat');
            this.log('Heartbeat sent');
        }
    }
    
    /**
     * Request friends status update
     */
    requestFriendsStatus() {
        if (this.socket && this.isConnected) {
            this.socket.emit('get_friends_status');
            this.log('Requested friends status');
        }
    }
    
    /**
     * Handle friend status change event
     */
    handleFriendStatusChange(data) {
        const { username, is_online } = data;
        
        // Notify all registered status callbacks
        if (this.statusCallbacks.has(username)) {
            const callbacks = this.statusCallbacks.get(username);
            callbacks.forEach(callback => {
                try {
                    callback(is_online, data);
                } catch (error) {
                    this.error('Error in status callback:', error);
                }
            });
        }
        
        // Notify friends status callbacks
        this.friendsStatusCallbacks.forEach(callback => {
            try {
                callback({ [username]: data });
            } catch (error) {
                this.error('Error in friends status callback:', error);
            }
        });
    }
    
    /**
     * Handle friends status update event
     */
    handleFriendsStatusUpdate(data) {
        const { friends_status } = data;
        
        // Notify all friends status callbacks
        this.friendsStatusCallbacks.forEach(callback => {
            try {
                callback(friends_status);
            } catch (error) {
                this.error('Error in friends status callback:', error);
            }
        });
    }
    
    /**
     * Register a callback for a specific user's status changes
     */
    onUserStatusChange(username, callback) {
        if (!this.statusCallbacks.has(username)) {
            this.statusCallbacks.set(username, new Set());
        }
        this.statusCallbacks.get(username).add(callback);
        
        this.log(`Registered status callback for user: ${username}`);
    }
    
    /**
     * Unregister a callback for a specific user's status changes
     */
    offUserStatusChange(username, callback) {
        if (this.statusCallbacks.has(username)) {
            this.statusCallbacks.get(username).delete(callback);
            
            // Clean up empty sets
            if (this.statusCallbacks.get(username).size === 0) {
                this.statusCallbacks.delete(username);
            }
        }
        
        this.log(`Unregistered status callback for user: ${username}`);
    }
    
    /**
     * Register a callback for friends status updates
     */
    onFriendsStatusUpdate(callback) {
        this.friendsStatusCallbacks.add(callback);
        this.log('Registered friends status callback');
    }
    
    /**
     * Unregister a callback for friends status updates
     */
    offFriendsStatusUpdate(callback) {
        this.friendsStatusCallbacks.delete(callback);
        this.log('Unregistered friends status callback');
    }
    
    /**
     * Disconnect from the presence WebSocket
     */
    disconnect() {
        this.log('Disconnecting from presence WebSocket');
        
        this.stopHeartbeat();
        
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }
        
        this.isConnected = false;
    }
    
    /**
     * Get connection status
     */
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts
        };
    }
    
    /**
     * Log debug messages
     */
    log(...args) {
        if (this.debug) {
            console.log('[PresenceManager]', ...args);
        }
    }
    
    /**
     * Log error messages
     */
    error(...args) {
        console.error('[PresenceManager]', ...args);
    }
}

// Create global instance
window.presenceManager = new PresenceManager();

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if user is authenticated and Socket.IO is available
    if (document.body.dataset.authenticated === 'true') {
        // Check if Socket.IO is available before initializing
        if (typeof io !== 'undefined') {
            window.presenceManager.init({
                debug: false // Set to true for debugging
            });
        } else {
            console.warn('[PresenceManager] Socket.IO not available, presence functionality will be disabled');
        }
    }
});
