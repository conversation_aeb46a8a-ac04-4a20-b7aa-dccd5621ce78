/* Profile Popup CSS - Compact profile tooltip for friend avatars */

.profile-popup {
    position: fixed;
    z-index: 9999;
    background: var(--slate-800, #1e293b);
    border: 1px solid var(--slate-600, #475569);
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
    padding: 8px;
    width: 280px;
    max-width: 90vw;
    max-height: 400px;
    overflow: hidden;
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    backdrop-filter: blur(8px);
}

.profile-popup.show {
    opacity: 1;
    transform: scale(1) translateY(0);
    pointer-events: auto;
}

.profile-popup.hide {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
    pointer-events: none;
}

/* Profile popup header */
.profile-popup-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
    border-radius: 8px;
    padding: 12px;
}

.profile-popup-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--slate-600, #475569);
    flex-shrink: 0;
}

.profile-popup-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-popup-avatar .avatar-initials {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--slate-600, #475569), var(--slate-700, #334155));
    color: white;
    font-weight: 600;
    font-size: 18px;
}

.profile-popup-info {
    flex: 1;
    min-width: 0;
}

.profile-popup-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--slate-200, #e2e8f0);
    margin: 0 0 2px 0;
    line-height: 1.2;
    word-break: break-word;
}

.profile-popup-username {
    font-size: 14px;
    color: var(--slate-400, #94a3b8);
    margin: 0;
    line-height: 1.2;
}

/* About me section */
.profile-popup-about {
    border-top: 1px solid var(--slate-700, #334155);
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
}

.profile-popup-about-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--slate-400, #94a3b8);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0 0 8px 0;
}

.profile-popup-about-content {
    font-size: 14px;
    color: var(--slate-300, #cbd5e1);
    line-height: 1.4;
    margin: 0;
    max-height: 200px;
    overflow-y: auto;
    word-break: break-word;
    scrollbar-width: thin;
    scrollbar-color: var(--slate-600, #475569) transparent;
}

.profile-popup-about-content::-webkit-scrollbar {
    width: 4px;
}

.profile-popup-about-content::-webkit-scrollbar-track {
    background: transparent;
}

.profile-popup-about-content::-webkit-scrollbar-thumb {
    background-color: var(--slate-600, #475569);
    border-radius: 2px;
}

.profile-popup-about-content::-webkit-scrollbar-thumb:hover {
    background-color: var(--slate-500, #64748b);
}

/* Empty state */
.profile-popup-about-empty {
    font-style: italic;
    color: var(--slate-500, #64748b);
}

/* Loading state */
.profile-popup-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: var(--slate-400, #94a3b8);
}

.profile-popup-loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid var(--slate-600, #475569);
    border-top: 2px solid var(--slate-400, #94a3b8);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error state */
.profile-popup-error {
    color: var(--red-400, #f87171);
    font-size: 14px;
    text-align: center;
    padding: 12px;
}

/* Theme-specific colors - Enhanced for all themes */

/* Purple Theme */
.theme-purple .profile-popup {
    background: linear-gradient(135deg, rgba(88, 28, 135, 0.95) 0%, rgba(76, 29, 149, 0.95) 100%);
    border: 1px solid rgba(124, 58, 237, 0.6);
    box-shadow: 0 20px 25px -5px rgba(124, 58, 237, 0.4), 0 10px 10px -5px rgba(124, 58, 237, 0.2);
    backdrop-filter: blur(12px);
}

.theme-purple .profile-popup::before {
    border-bottom-color: rgba(88, 28, 135, 0.95);
    border-top-color: rgba(88, 28, 135, 0.95);
    border-left-color: rgba(88, 28, 135, 0.95);
    border-right-color: rgba(88, 28, 135, 0.95);
}

.theme-purple .profile-popup-about {
    border-top-color: rgba(109, 40, 217, 0.4);
    background: rgba(124, 58, 237, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
}

.theme-purple .profile-popup-header {
    background: rgba(124, 58, 237, 0.15);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
}

.theme-purple .profile-popup-avatar {
    border-color: rgba(124, 58, 237, 0.8);
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.3);
}

.theme-purple .profile-popup-name {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(124, 58, 237, 0.5);
}

.theme-purple .profile-popup-username {
    color: rgba(196, 181, 253, 0.8);
}

.theme-purple .profile-popup-about-title {
    color: rgba(196, 181, 253, 0.9);
}

.theme-purple .profile-popup-about-content {
    color: rgba(255, 255, 255, 0.9);
}

/* Blue Theme */
.theme-blue .profile-popup {
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.95) 0%, rgba(30, 64, 175, 0.95) 100%);
    border: 1px solid rgba(59, 130, 246, 0.6);
    box-shadow: 0 20px 25px -5px rgba(59, 130, 246, 0.4), 0 10px 10px -5px rgba(59, 130, 246, 0.2);
    backdrop-filter: blur(12px);
}

.theme-blue .profile-popup::before {
    border-bottom-color: rgba(30, 58, 138, 0.95);
    border-top-color: rgba(30, 58, 138, 0.95);
    border-left-color: rgba(30, 58, 138, 0.95);
    border-right-color: rgba(30, 58, 138, 0.95);
}

.theme-blue .profile-popup-about {
    border-top-color: rgba(37, 99, 235, 0.4);
    background: rgba(59, 130, 246, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
}

.theme-blue .profile-popup-header {
    background: rgba(59, 130, 246, 0.15);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
}

.theme-blue .profile-popup-avatar {
    border-color: rgba(59, 130, 246, 0.8);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.theme-blue .profile-popup-name {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(59, 130, 246, 0.5);
}

.theme-blue .profile-popup-username {
    color: rgba(147, 197, 253, 0.8);
}

.theme-blue .profile-popup-about-title {
    color: rgba(147, 197, 253, 0.9);
}

.theme-blue .profile-popup-about-content {
    color: rgba(255, 255, 255, 0.9);
}

/* Green Theme */
.theme-green .profile-popup {
    background: linear-gradient(135deg, rgba(20, 83, 45, 0.95) 0%, rgba(22, 101, 52, 0.95) 100%);
    border: 1px solid rgba(34, 197, 94, 0.6);
    box-shadow: 0 20px 25px -5px rgba(34, 197, 94, 0.4), 0 10px 10px -5px rgba(34, 197, 94, 0.2);
    backdrop-filter: blur(12px);
}

.theme-green .profile-popup::before {
    border-bottom-color: rgba(20, 83, 45, 0.95);
    border-top-color: rgba(20, 83, 45, 0.95);
    border-left-color: rgba(20, 83, 45, 0.95);
    border-right-color: rgba(20, 83, 45, 0.95);
}

.theme-green .profile-popup-about {
    border-top-color: rgba(22, 163, 74, 0.4);
    background: rgba(34, 197, 94, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
}

.theme-green .profile-popup-header {
    background: rgba(34, 197, 94, 0.15);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
}

.theme-green .profile-popup-avatar {
    border-color: rgba(34, 197, 94, 0.8);
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.3);
}

.theme-green .profile-popup-name {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(34, 197, 94, 0.5);
}

.theme-green .profile-popup-username {
    color: rgba(134, 239, 172, 0.8);
}

.theme-green .profile-popup-about-title {
    color: rgba(134, 239, 172, 0.9);
}

.theme-green .profile-popup-about-content {
    color: rgba(255, 255, 255, 0.9);
}

/* Orange Theme */
.theme-orange .profile-popup {
    background: linear-gradient(135deg, rgba(154, 52, 18, 0.95) 0%, rgba(194, 65, 12, 0.95) 100%);
    border: 1px solid rgba(249, 115, 22, 0.6);
    box-shadow: 0 20px 25px -5px rgba(249, 115, 22, 0.4), 0 10px 10px -5px rgba(249, 115, 22, 0.2);
    backdrop-filter: blur(12px);
}

.theme-orange .profile-popup::before {
    border-bottom-color: rgba(154, 52, 18, 0.95);
    border-top-color: rgba(154, 52, 18, 0.95);
    border-left-color: rgba(154, 52, 18, 0.95);
    border-right-color: rgba(154, 52, 18, 0.95);
}

.theme-orange .profile-popup-about {
    border-top-color: rgba(234, 88, 12, 0.4);
    background: rgba(249, 115, 22, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
}

.theme-orange .profile-popup-header {
    background: rgba(249, 115, 22, 0.15);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
}

.theme-orange .profile-popup-avatar {
    border-color: rgba(249, 115, 22, 0.8);
    box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.3);
}

.theme-orange .profile-popup-name {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(249, 115, 22, 0.5);
}

.theme-orange .profile-popup-username {
    color: rgba(254, 215, 170, 0.8);
}

.theme-orange .profile-popup-about-title {
    color: rgba(254, 215, 170, 0.9);
}

.theme-orange .profile-popup-about-content {
    color: rgba(255, 255, 255, 0.9);
}

/* Pink Theme */
.theme-pink .profile-popup {
    background: linear-gradient(135deg, rgba(131, 24, 67, 0.95) 0%, rgba(190, 24, 93, 0.95) 100%);
    border: 1px solid rgba(236, 72, 153, 0.6);
    box-shadow: 0 20px 25px -5px rgba(236, 72, 153, 0.4), 0 10px 10px -5px rgba(236, 72, 153, 0.2);
    backdrop-filter: blur(12px);
}

.theme-pink .profile-popup::before {
    border-bottom-color: rgba(131, 24, 67, 0.95);
    border-top-color: rgba(131, 24, 67, 0.95);
    border-left-color: rgba(131, 24, 67, 0.95);
    border-right-color: rgba(131, 24, 67, 0.95);
}

.theme-pink .profile-popup-about {
    border-top-color: rgba(219, 39, 119, 0.4);
    background: rgba(236, 72, 153, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
}

.theme-pink .profile-popup-header {
    background: rgba(236, 72, 153, 0.15);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
}

.theme-pink .profile-popup-avatar {
    border-color: rgba(236, 72, 153, 0.8);
    box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.3);
}

.theme-pink .profile-popup-name {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(236, 72, 153, 0.5);
}

.theme-pink .profile-popup-username {
    color: rgba(251, 207, 232, 0.8);
}

.theme-pink .profile-popup-about-title {
    color: rgba(251, 207, 232, 0.9);
}

.theme-pink .profile-popup-about-content {
    color: rgba(255, 255, 255, 0.9);
}

/* Cyan Theme */
.theme-cyan .profile-popup {
    background: linear-gradient(135deg, rgba(22, 78, 99, 0.95) 0%, rgba(8, 145, 178, 0.95) 100%);
    border: 1px solid rgba(6, 182, 212, 0.6);
    box-shadow: 0 20px 25px -5px rgba(6, 182, 212, 0.4), 0 10px 10px -5px rgba(6, 182, 212, 0.2);
    backdrop-filter: blur(12px);
}

.theme-cyan .profile-popup::before {
    border-bottom-color: rgba(22, 78, 99, 0.95);
    border-top-color: rgba(22, 78, 99, 0.95);
    border-left-color: rgba(22, 78, 99, 0.95);
    border-right-color: rgba(22, 78, 99, 0.95);
}

.theme-cyan .profile-popup-about {
    border-top-color: rgba(8, 145, 178, 0.4);
    background: rgba(6, 182, 212, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
}

.theme-cyan .profile-popup-header {
    background: rgba(6, 182, 212, 0.15);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
}

.theme-cyan .profile-popup-avatar {
    border-color: rgba(6, 182, 212, 0.8);
    box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.3);
}

.theme-cyan .profile-popup-name {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(6, 182, 212, 0.5);
}

.theme-cyan .profile-popup-username {
    color: rgba(165, 243, 252, 0.8);
}

.theme-cyan .profile-popup-about-title {
    color: rgba(165, 243, 252, 0.9);
}

.theme-cyan .profile-popup-about-content {
    color: rgba(255, 255, 255, 0.9);
}

/* Red Theme */
.theme-red .profile-popup {
    background: linear-gradient(135deg, rgba(127, 29, 29, 0.95) 0%, rgba(153, 27, 27, 0.95) 100%);
    border: 1px solid rgba(239, 68, 68, 0.6);
    box-shadow: 0 20px 25px -5px rgba(239, 68, 68, 0.4), 0 10px 10px -5px rgba(239, 68, 68, 0.2);
    backdrop-filter: blur(12px);
}

.theme-red .profile-popup::before {
    border-bottom-color: rgba(127, 29, 29, 0.95);
    border-top-color: rgba(127, 29, 29, 0.95);
    border-left-color: rgba(127, 29, 29, 0.95);
    border-right-color: rgba(127, 29, 29, 0.95);
}

.theme-red .profile-popup-about {
    border-top-color: rgba(220, 38, 38, 0.4);
    background: rgba(239, 68, 68, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
}

.theme-red .profile-popup-header {
    background: rgba(239, 68, 68, 0.15);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
}

.theme-red .profile-popup-avatar {
    border-color: rgba(239, 68, 68, 0.8);
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3);
}

.theme-red .profile-popup-name {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(239, 68, 68, 0.5);
}

.theme-red .profile-popup-username {
    color: rgba(254, 202, 202, 0.8);
}

.theme-red .profile-popup-about-title {
    color: rgba(254, 202, 202, 0.9);
}

.theme-red .profile-popup-about-content {
    color: rgba(255, 255, 255, 0.9);
}

/* Yellow Theme */
.theme-yellow .profile-popup {
    background: linear-gradient(135deg, rgba(113, 63, 18, 0.95) 0%, rgba(146, 64, 14, 0.95) 100%);
    border: 1px solid rgba(234, 179, 8, 0.6);
    box-shadow: 0 20px 25px -5px rgba(234, 179, 8, 0.4), 0 10px 10px -5px rgba(234, 179, 8, 0.2);
    backdrop-filter: blur(12px);
}

.theme-yellow .profile-popup::before {
    border-bottom-color: rgba(113, 63, 18, 0.95);
    border-top-color: rgba(113, 63, 18, 0.95);
    border-left-color: rgba(113, 63, 18, 0.95);
    border-right-color: rgba(113, 63, 18, 0.95);
}

.theme-yellow .profile-popup-about {
    border-top-color: rgba(202, 138, 4, 0.4);
    background: rgba(234, 179, 8, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
}

.theme-yellow .profile-popup-header {
    background: rgba(234, 179, 8, 0.15);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
}

.theme-yellow .profile-popup-avatar {
    border-color: rgba(234, 179, 8, 0.8);
    box-shadow: 0 0 0 2px rgba(234, 179, 8, 0.3);
}

.theme-yellow .profile-popup-name {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(234, 179, 8, 0.5);
}

.theme-yellow .profile-popup-username {
    color: rgba(254, 240, 138, 0.8);
}

.theme-yellow .profile-popup-about-title {
    color: rgba(254, 240, 138, 0.9);
}

.theme-yellow .profile-popup-about-content {
    color: rgba(255, 255, 255, 0.9);
}

/* Indigo Theme */
.theme-indigo .profile-popup {
    background: linear-gradient(135deg, rgba(49, 46, 129, 0.95) 0%, rgba(55, 48, 163, 0.95) 100%);
    border: 1px solid rgba(99, 102, 241, 0.6);
    box-shadow: 0 20px 25px -5px rgba(99, 102, 241, 0.4), 0 10px 10px -5px rgba(99, 102, 241, 0.2);
    backdrop-filter: blur(12px);
}

.theme-indigo .profile-popup::before {
    border-bottom-color: rgba(49, 46, 129, 0.95);
    border-top-color: rgba(49, 46, 129, 0.95);
    border-left-color: rgba(49, 46, 129, 0.95);
    border-right-color: rgba(49, 46, 129, 0.95);
}

.theme-indigo .profile-popup-about {
    border-top-color: rgba(79, 70, 229, 0.4);
    background: rgba(99, 102, 241, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
}

.theme-indigo .profile-popup-header {
    background: rgba(99, 102, 241, 0.15);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
}

.theme-indigo .profile-popup-avatar {
    border-color: rgba(99, 102, 241, 0.8);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.3);
}

.theme-indigo .profile-popup-name {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(99, 102, 241, 0.5);
}

.theme-indigo .profile-popup-username {
    color: rgba(199, 210, 254, 0.8);
}

.theme-indigo .profile-popup-about-title {
    color: rgba(199, 210, 254, 0.9);
}

.theme-indigo .profile-popup-about-content {
    color: rgba(255, 255, 255, 0.9);
}

/* Teal Theme */
.theme-teal .profile-popup {
    background: linear-gradient(135deg, rgba(19, 78, 74, 0.95) 0%, rgba(17, 94, 89, 0.95) 100%);
    border: 1px solid rgba(20, 184, 166, 0.6);
    box-shadow: 0 20px 25px -5px rgba(20, 184, 166, 0.4), 0 10px 10px -5px rgba(20, 184, 166, 0.2);
    backdrop-filter: blur(12px);
}

.theme-teal .profile-popup::before {
    border-bottom-color: rgba(19, 78, 74, 0.95);
    border-top-color: rgba(19, 78, 74, 0.95);
    border-left-color: rgba(19, 78, 74, 0.95);
    border-right-color: rgba(19, 78, 74, 0.95);
}

.theme-teal .profile-popup-about {
    border-top-color: rgba(13, 148, 136, 0.4);
    background: rgba(20, 184, 166, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
}

.theme-teal .profile-popup-header {
    background: rgba(20, 184, 166, 0.15);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
}

.theme-teal .profile-popup-avatar {
    border-color: rgba(20, 184, 166, 0.8);
    box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.3);
}

.theme-teal .profile-popup-name {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(20, 184, 166, 0.5);
}

.theme-teal .profile-popup-username {
    color: rgba(153, 246, 228, 0.8);
}

.theme-teal .profile-popup-about-title {
    color: rgba(153, 246, 228, 0.9);
}

.theme-teal .profile-popup-about-content {
    color: rgba(255, 255, 255, 0.9);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .profile-popup {
        width: 260px;
        padding: 14px;
        max-height: 350px;
    }
    
    .profile-popup-avatar {
        width: 40px;
        height: 40px;
    }
    
    .profile-popup-name {
        font-size: 15px;
    }
    
    .profile-popup-username {
        font-size: 13px;
    }
    
    .profile-popup-about-content {
        font-size: 13px;
        max-height: 150px;
    }
}

/* Animation for popup arrow (optional enhancement) */
.profile-popup::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    pointer-events: none;
}

.profile-popup.arrow-top::before {
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid var(--slate-800, #1e293b);
}

.profile-popup.arrow-bottom::before {
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid var(--slate-800, #1e293b);
}

.profile-popup.arrow-left::before {
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid var(--slate-800, #1e293b);
}

.profile-popup.arrow-right::before {
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid var(--slate-800, #1e293b);
}
