from flask import request, jsonify
from flask_login import login_required, current_user
from models.profile_customization import ProfileCustomization
from models.user import User
from api.profile import profile_api
import json

@profile_api.route('/customization', methods=['GET'])
@login_required
def get_profile_customization():
    """Get current user's profile customization"""
    profile = ProfileCustomization.get_or_create(current_user)
    
    return jsonify({
        'success': True,
        'profile': {
            'background_color': profile.background_color,
            'text_color': profile.text_color,
            'accent_color': profile.accent_color,
            'profile_container_color': profile.profile_container_color,
            'profile_header_color': profile.profile_header_color,
            'profile_about_color': profile.profile_about_color,
            'profile_content_color': profile.profile_content_color,
            'profile_friends_color': profile.profile_friends_color,
            'background_type': profile.background_type,
            'background_gradient': profile.background_gradient,
            'background_image': profile.background_image,
            'background_pattern': profile.background_pattern,
            'layout_type': profile.layout_type,
            'font_family': profile.font_family,
            'heading_font': profile.heading_font,
            'show_friends': profile.show_friends,
            'show_activity': profile.show_activity,
            'show_stats': profile.show_stats,
            'custom_css': profile.custom_css,
            'custom_sections': profile.custom_sections,
            'is_public': profile.is_public,
            'social_links': profile.social_links
        }
    })

@profile_api.route('/customization/<username>', methods=['GET'])
def get_user_profile_customization(username):
    """Get a specific user's profile customization"""
    user = User.objects(username=username).first()
    if not user:
        return jsonify({'success': False, 'error': 'User not found'}), 404
    
    profile = ProfileCustomization.get_or_create(user)
    
    # Check if profile is private and not the current user
    if not profile.is_public and (not current_user.is_authenticated or current_user.id != user.id):
        return jsonify({
            'success': True,
            'profile': {
                'is_public': False
            }
        })
    
    return jsonify({
        'success': True,
        'profile': {
            'background_color': profile.background_color,
            'text_color': profile.text_color,
            'accent_color': profile.accent_color,
            'profile_container_color': profile.profile_container_color,
            'profile_header_color': profile.profile_header_color,
            'profile_about_color': profile.profile_about_color,
            'profile_content_color': profile.profile_content_color,
            'profile_friends_color': profile.profile_friends_color,
            'background_type': profile.background_type,
            'background_gradient': profile.background_gradient,
            'background_image': profile.background_image,
            'background_pattern': profile.background_pattern,
            'layout_type': profile.layout_type,
            'font_family': profile.font_family,
            'heading_font': profile.heading_font,
            'show_friends': profile.show_friends,
            'show_activity': profile.show_activity,
            'show_stats': profile.show_stats,
            'custom_css': profile.custom_css,
            'custom_sections': profile.custom_sections,
            'is_public': profile.is_public,
            'social_links': profile.social_links
        }
    })

@profile_api.route('/customization', methods=['POST'])
@login_required
def update_profile_customization():
    """Update current user's profile customization"""
    data = request.json
    print(f"[DEBUG] Received profile customization data: {data}")  # Debug log
    profile = ProfileCustomization.get_or_create(current_user)
    
    # Update user display name (Also known as)
    if 'display_name' in data:
        current_user.display_name = data['display_name']
        current_user.save()
    
    # Update basic customization
    if 'background_color' in data:
        profile.background_color = data['background_color']
    if 'text_color' in data:
        profile.text_color = data['text_color']
    if 'accent_color' in data:
        profile.accent_color = data['accent_color']
        
    # Update profile section colors
    if 'profile_container_color' in data:
        profile.profile_container_color = data['profile_container_color']
    if 'profile_header_color' in data:
        profile.profile_header_color = data['profile_header_color']
    if 'profile_about_color' in data:
        profile.profile_about_color = data['profile_about_color']
    if 'profile_content_color' in data:
        profile.profile_content_color = data['profile_content_color']
    if 'profile_friends_color' in data:
        profile.profile_friends_color = data['profile_friends_color']
    
    # Update background options
    if 'background_type' in data:
        profile.background_type = data['background_type']
    if 'background_gradient' in data:
        profile.background_gradient = data['background_gradient']
    if 'background_image' in data:
        profile.background_image = data['background_image']
    if 'background_pattern' in data:
        profile.background_pattern = data['background_pattern']
    
    # Update layout options
    if 'layout_type' in data:
        profile.layout_type = data['layout_type']
    
    # Update font options
    if 'font_family' in data:
        profile.font_family = data['font_family']
    if 'heading_font' in data:
        profile.heading_font = data['heading_font']
    
    # Update content display options - convert to boolean
    if 'show_friends' in data:
        profile.show_friends = bool(data['show_friends'] == 'true' or data['show_friends'] == True)
    if 'show_activity' in data:
        profile.show_activity = bool(data['show_activity'] == 'true' or data['show_activity'] == True)
    if 'show_stats' in data:
        profile.show_stats = bool(data['show_stats'] == 'true' or data['show_stats'] == True)
    
    # Update custom CSS
    if 'custom_css' in data:
        profile.custom_css = data['custom_css']
    
    # Update custom sections
    if 'custom_sections' in data:
        profile.custom_sections = data['custom_sections']
    
    # Update profile visibility - convert to boolean
    if 'is_public' in data:
        profile.is_public = bool(data['is_public'] == 'true' or data['is_public'] == True)
    
    # Update social links
    if 'social_links' in data:
        print(f"[DEBUG] Updating social links from: {profile.social_links} to: {data['social_links']}")  # Debug log
        profile.social_links = data['social_links']
        print(f"[DEBUG] Social links updated successfully")  # Debug log
    
    profile.save()
    
    return jsonify({
        'success': True,
        'message': 'Profile customization updated successfully'
    })