/**
 * Profile Edit Page JavaScript
 * Handles interactive functionality for the profile edit page
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize color pickers
    initColorPickers();
    
    // Initialize social links functionality
    initSocialLinks();
    
    // We've replaced custom sections with a fixed About Me section
    
    // Initialize form submission
    initFormSubmission();
    
    // Initialize preview functionality
    initPreview();
    
    // Initialize profile picture upload
    initProfilePictureUpload();
    
    // Initialize reset colors button
    initResetColorsButton();
    
    // Initialize background preview
    updateBackgroundPreview();
});

/**
 * Initialize color pickers with preview functionality
 */
function initColorPickers() {
    const colorInputs = document.querySelectorAll('input[type="color"]');
    
    colorInputs.forEach(input => {
        const preview = input.previousElementSibling;
        
        // Set initial preview color
        if (preview && preview.classList.contains('color-preview')) {
            preview.style.backgroundColor = input.value;
        }
        
        // Update preview on change
        input.addEventListener('input', function() {
            if (preview) {
                preview.style.backgroundColor = this.value;
            }
            
            // Update the background preview for any profile color change
            if (this.id.includes('profile_') || this.id === 'text_color' || this.id === 'accent_color') {
                updateBackgroundPreview();
            }
        });
        
        // Handle click on preview to open color picker
        if (preview) {
            preview.addEventListener('click', function() {
                input.click();
            });
        }
    });
}

/**
 * Initialize social links functionality
 */
function initSocialLinks() {
    const socialLinksContainer = document.getElementById('social-links-container');
    const addSocialLinkBtn = document.getElementById('add-social-link');

    // Debug logs can be enabled for troubleshooting
    // console.log('Initializing social links...', { socialLinksContainer, addSocialLinkBtn });

    if (!socialLinksContainer) {
        console.warn('Social links container not found');
        return;
    }

    if (!addSocialLinkBtn) {
        console.warn('Add social link button not found');
        return;
    }

    // Add new social link
    addSocialLinkBtn.addEventListener('click', function() {
        const linkItem = createSocialLinkItem();
        socialLinksContainer.appendChild(linkItem);
    });

    // Handle existing social link removals using event delegation
    socialLinksContainer.addEventListener('click', function(e) {
        // Check if the clicked element or any of its parents has the class 'social-link-remove'
        const removeButton = e.target.closest('.social-link-remove');

        if (removeButton) {
            e.preventDefault();
            e.stopPropagation();

            const linkItem = removeButton.closest('.social-link-item');

            if (linkItem) {
                // Add fade-out animation
                linkItem.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                linkItem.style.opacity = '0';
                linkItem.style.transform = 'translateY(10px)';

                // Remove after animation completes
                setTimeout(() => {
                    linkItem.remove();
                }, 300);
            } else {
                console.warn('Could not find parent social link item');
            }
        }
    });

    // Initialize existing social links with proper styling and event handlers
    initializeExistingSocialLinks();
}

/**
 * Initialize existing social links with proper event handlers and styling
 */
function initializeExistingSocialLinks() {
    const existingSocialLinks = document.querySelectorAll('.social-link-item');

    existingSocialLinks.forEach((linkItem) => {
        // Ensure proper transition styles
        linkItem.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

        // Initialize platform change handler for existing links
        const platformSelect = linkItem.querySelector('.social-link-platform');
        const iconElement = linkItem.querySelector('.social-link-icon i');
        const removeButton = linkItem.querySelector('.social-link-remove');

        if (platformSelect && iconElement) {
            platformSelect.addEventListener('change', function() {
                updateSocialIcon(this.value, iconElement);
            });

            // Set initial icon based on current selection
            updateSocialIcon(platformSelect.value, iconElement);
        }

        // Ensure remove button is properly styled and accessible
        if (removeButton) {
            removeButton.style.cursor = 'pointer';
            removeButton.style.pointerEvents = 'auto';
        }
    });
}

/**
 * Create a new social link item element
 */
function createSocialLinkItem() {
    const linkItem = document.createElement('div');
    linkItem.className = 'social-link-item';
    linkItem.style.opacity = '0';
    linkItem.style.transform = 'translateY(10px)';
    linkItem.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

    linkItem.innerHTML = `
        <div class="social-link-icon">
            <i class="fas fa-link"></i>
        </div>
        <select class="form-input social-link-platform" name="social_platform[]">
            <option value="Twitter">Twitter</option>
            <option value="Instagram">Instagram</option>
            <option value="Facebook">Facebook</option>
            <option value="LinkedIn">LinkedIn</option>
            <option value="GitHub">GitHub</option>
            <option value="YouTube">YouTube</option>
            <option value="Twitch">Twitch</option>
            <option value="Discord">Discord</option>
            <option value="TikTok">TikTok</option>
            <option value="Reddit">Reddit</option>
            <option value="Other">Other</option>
        </select>
        <input type="url" class="form-input social-link-url" name="social_url[]" placeholder="https://">
        <button type="button" class="social-link-remove" aria-label="Remove social link">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Animate in
    setTimeout(() => {
        linkItem.style.opacity = '1';
        linkItem.style.transform = 'translateY(0)';
    }, 10);

    // Update icon when platform changes
    const platformSelect = linkItem.querySelector('.social-link-platform');
    const iconElement = linkItem.querySelector('.social-link-icon i');

    platformSelect.addEventListener('change', function() {
        updateSocialIcon(this.value, iconElement);
    });

    // Note: Remove button event handling is now done via event delegation in initSocialLinks()
    // This ensures consistent behavior for both existing and new social links

    return linkItem;
}

/**
 * Update social icon based on platform
 */
function updateSocialIcon(platform, iconElement) {
    const platformLower = platform.toLowerCase();
    let iconClass = 'fa-link';
    
    if (platformLower === 'twitter' || platformLower === 'x') {
        iconClass = 'fa-x-twitter';
    } else if (platformLower === 'instagram') {
        iconClass = 'fa-instagram';
    } else if (platformLower === 'facebook') {
        iconClass = 'fa-facebook';
    } else if (platformLower === 'linkedin') {
        iconClass = 'fa-linkedin';
    } else if (platformLower === 'github') {
        iconClass = 'fa-github';
    } else if (platformLower === 'youtube') {
        iconClass = 'fa-youtube';
    } else if (platformLower === 'twitch') {
        iconClass = 'fa-twitch';
    } else if (platformLower === 'discord') {
        iconClass = 'fa-discord';
    } else if (platformLower === 'tiktok') {
        iconClass = 'fa-tiktok';
    } else if (platformLower === 'reddit') {
        iconClass = 'fa-reddit';
    }
    
    // Remove all fa classes and add the new one
    iconElement.className = '';
    iconElement.classList.add('fab', iconClass);
}

// Custom sections functionality has been replaced with a fixed About Me section

/**
 * Initialize form submission with validation and AJAX
 */
function initFormSubmission() {
    const profileForm = document.getElementById('profile-edit-form');
    
    if (!profileForm) return;
    
    profileForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        const submitBtn = profileForm.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<span class="loading-indicator"></span> Saving...';
        submitBtn.disabled = true;
        
        // Collect form data
        const formData = new FormData(profileForm);
        
        // Convert to JSON
        const jsonData = {};
        
        // First handle checkboxes - set all boolean fields to false by default
        // This ensures unchecked boxes are properly handled
        ['show_activity', 'show_friends', 'show_stats', 'is_public'].forEach(key => {
            jsonData[key] = false;
        });
        
        // Now process all form data
        for (const [key, value] of formData.entries()) {
            // Handle arrays (social links, custom sections)
            if (key.endsWith('[]')) {
                const cleanKey = key.slice(0, -2);
                if (!jsonData[cleanKey]) {
                    jsonData[cleanKey] = [];
                }
                jsonData[cleanKey].push(value);
            } 
            // Handle checkboxes - if present in the form data, they're checked
            else if (['show_activity', 'show_friends', 'show_stats', 'is_public'].includes(key)) {
                jsonData[key] = true;
            }
            // Handle all other fields
            else {
                jsonData[key] = value;
            }
        }
        
        // Process social links into an object
        // Always create social_links object, even if empty (to handle removals)
        jsonData.social_links = {};

        if (jsonData.social_platform && jsonData.social_url) {
            for (let i = 0; i < jsonData.social_platform.length; i++) {
                const platform = jsonData.social_platform[i];
                const url = jsonData.social_url[i];
                if (platform && url) {
                    jsonData.social_links[platform] = url;
                }
            }
        }

        // Clean up the array fields
        delete jsonData.social_platform;
        delete jsonData.social_url;

        console.log('Processed social links:', jsonData.social_links); // Debug log

        // Process About Me content into custom_sections object
        if (jsonData.about_me_content) {
            jsonData.custom_sections = {
                about: jsonData.about_me_content
            };
            delete jsonData.about_me_content;
        } else {
            jsonData.custom_sections = {
                about: ""
            };
        }

        console.log('Final form data being sent to backend:', jsonData); // Debug log

        // Send AJAX request
        fetch('/api/profile/customization', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(jsonData),
        })
        .then(response => response.json())
        .then(data => {
            // Reset button state
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
            
            // Show success message
            showMessage('success', 'Profile updated successfully!');
            
            // Redirect to profile page after a delay
            setTimeout(() => {
                window.location.href = '/profile/' + jsonData.username;
            }, 1500);
        })
        .catch(error => {
            // Reset button state
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
            
            // Show error message
            showMessage('error', 'Error updating profile. Please try again.');
            console.error('Error:', error);
        });
    });
}

/**
 * Show a message to the user
 */
function showMessage(type, text) {
    // Remove any existing messages
    const existingMessages = document.querySelectorAll('.message');
    existingMessages.forEach(msg => msg.remove());
    
    // Create new message
    const message = document.createElement('div');
    message.className = `message message-${type}`;
    
    const icon = type === 'success' ? 'check-circle' : 'exclamation-circle';
    message.innerHTML = `
        <div class="message-icon">
            <i class="fas fa-${icon}"></i>
        </div>
        <div class="message-text">${text}</div>
    `;
    
    // Add to page
    const container = document.querySelector('.edit-container');
    container.insertBefore(message, container.firstChild);
    
    // Animate in
    message.style.opacity = '0';
    message.style.transform = 'translateY(-10px)';
    setTimeout(() => {
        message.style.opacity = '1';
        message.style.transform = 'translateY(0)';
    }, 10);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        message.style.opacity = '0';
        message.style.transform = 'translateY(-10px)';
        setTimeout(() => {
            message.remove();
        }, 300);
    }, 5000);
}

/**
 * Initialize preview functionality
 */
function initPreview() {
    const previewBtn = document.getElementById('preview-button');
    const previewFrame = document.getElementById('preview-frame');
    
    if (!previewBtn || !previewFrame) return;
    
    previewBtn.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Collect form data for preview
        const formData = new FormData(document.getElementById('profile-edit-form'));
        
        // Build query string for preview
        const params = new URLSearchParams();
        params.append('profile_container_color', formData.get('profile_container_color'));
        params.append('profile_header_color', formData.get('profile_header_color'));
        params.append('profile_about_color', formData.get('profile_about_color'));
        params.append('profile_content_color', formData.get('profile_content_color'));
        params.append('profile_friends_color', formData.get('profile_friends_color'));
        params.append('text_color', formData.get('text_color'));
        params.append('accent_color', formData.get('accent_color'));
        params.append('layout', formData.get('layout_type'));
        params.append('font', formData.get('font_family'));
        params.append('heading_font', formData.get('heading_font'));
        params.append('about_content', formData.get('about_me_content'));
        
        // Update preview iframe
        previewFrame.src = `/profile/preview?${params.toString()}`;
        
        // Scroll to preview
        previewFrame.scrollIntoView({ behavior: 'smooth' });
    });
}

/**
 * Initialize profile picture upload
 */
function initProfilePictureUpload() {
    const profilePictureInput = document.getElementById('profile-picture-input');
    const profilePictureImg = document.querySelector('.profile-picture-edit img');
    
    if (!profilePictureInput || !profilePictureImg) return;
    
    // Handle click on profile picture to trigger file input
    document.querySelector('.profile-picture-edit').addEventListener('click', function() {
        profilePictureInput.click();
    });
    
    // Handle file selection
    profilePictureInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const file = this.files[0];
            
            // Check file type
            if (!file.type.match('image.*')) {
                showMessage('error', 'Please select an image file.');
                return;
            }
            
            // Check file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                showMessage('error', 'Image size should be less than 5MB.');
                return;
            }
            
            // Preview the image
            const reader = new FileReader();
            reader.onload = function(e) {
                profilePictureImg.src = e.target.result;
            };
            reader.readAsDataURL(file);
            
            // Upload the image
            uploadProfilePicture(file);
        }
    });
}

/**
 * Upload profile picture to server
 */
function uploadProfilePicture(file) {
    const formData = new FormData();
    formData.append('profile_picture', file);
    
    fetch('/api/profile/avatar', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('success', 'Profile picture updated successfully!');
        } else {
            showMessage('error', data.error || 'Error uploading profile picture.');
        }
    })
    .catch(error => {
        showMessage('error', 'Error uploading profile picture. Please try again.');
        console.error('Error:', error);
    });
}

/**
 * Initialize reset colors button functionality
 */
function initResetColorsButton() {
    const resetButton = document.getElementById('reset-colors-button');
    if (!resetButton) return;
    
    resetButton.addEventListener('click', function() {
        // Default colors
        const defaultColors = {
            'profile_container_color': '#ffffff',
            'profile_header_color': '#f8f9fa',
            'profile_about_color': '#f8f9fa',
            'profile_content_color': '#ffffff',
            'profile_friends_color': '#f8f9fa',
            'text_color': '#212529',
            'accent_color': '#4f46e5'
        };
        
        // Reset each color input to its default value
        for (const [id, color] of Object.entries(defaultColors)) {
            const input = document.getElementById(id);
            if (input) {
                input.value = color;
                
                // Also update the color preview
                const preview = input.previousElementSibling;
                if (preview && preview.classList.contains('color-preview')) {
                    preview.style.backgroundColor = color;
                }
            }
        }
        
        // Update the background preview
        updateBackgroundPreview();
        
        // Show success message
        showMessage('success', 'Colors reset to default values');
    });
}

/**
 * Update profile section previews based on selected colors
 */
function updateBackgroundPreview() {
    const previewElement = document.getElementById('background-preview');
    if (!previewElement) return;
    
    // Get all the profile section colors
    const profileContainerColor = document.getElementById('profile_container_color').value;
    const profileHeaderColor = document.getElementById('profile_header_color').value;
    const profileAboutColor = document.getElementById('profile_about_color').value;
    const profileContentColor = document.getElementById('profile_content_color').value;
    const profileFriendsColor = document.getElementById('profile_friends_color').value;
    
    // Update the preview with a sample of the colors
    previewElement.style.background = `linear-gradient(to right, 
        ${profileContainerColor} 0%, 
        ${profileContainerColor} 20%, 
        ${profileHeaderColor} 20%, 
        ${profileHeaderColor} 40%, 
        ${profileAboutColor} 40%, 
        ${profileAboutColor} 60%, 
        ${profileContentColor} 60%, 
        ${profileContentColor} 80%, 
        ${profileFriendsColor} 80%, 
        ${profileFriendsColor} 100%)`;
}