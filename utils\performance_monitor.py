"""
Performance monitoring utilities for API endpoints
Provides detailed performance metrics and monitoring capabilities
"""

import time
import logging
import functools
from datetime import datetime
from typing import Dict, Any, Optional
from flask import request, g
from utils.redis_cache import cache

class PerformanceMonitor:
    """Performance monitoring and metrics collection"""
    
    def __init__(self):
        self.metrics = {}
        self.slow_query_threshold = 1000  # 1 second in milliseconds
        
    def start_timer(self, operation_name: str) -> str:
        """Start timing an operation"""
        timer_id = f"{operation_name}_{int(time.time() * 1000000)}"
        g.timers = getattr(g, 'timers', {})
        g.timers[timer_id] = {
            'operation': operation_name,
            'start_time': time.time(),
            'start_timestamp': datetime.utcnow()
        }
        return timer_id
    
    def end_timer(self, timer_id: str) -> float:
        """End timing an operation and return duration in milliseconds"""
        if not hasattr(g, 'timers') or timer_id not in g.timers:
            return 0.0
        
        timer = g.timers[timer_id]
        duration = (time.time() - timer['start_time']) * 1000  # Convert to milliseconds
        
        # Log slow operations
        if duration > self.slow_query_threshold:
            logging.warning(f"Slow operation detected: {timer['operation']} took {duration:.2f}ms")
        
        # Store metrics
        operation = timer['operation']
        if operation not in self.metrics:
            self.metrics[operation] = {
                'count': 0,
                'total_time': 0,
                'min_time': float('inf'),
                'max_time': 0,
                'slow_queries': 0
            }
        
        self.metrics[operation]['count'] += 1
        self.metrics[operation]['total_time'] += duration
        self.metrics[operation]['min_time'] = min(self.metrics[operation]['min_time'], duration)
        self.metrics[operation]['max_time'] = max(self.metrics[operation]['max_time'], duration)
        
        if duration > self.slow_query_threshold:
            self.metrics[operation]['slow_queries'] += 1
        
        del g.timers[timer_id]
        return duration
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        processed_metrics = {}
        for operation, data in self.metrics.items():
            if data['count'] > 0:
                processed_metrics[operation] = {
                    'count': data['count'],
                    'avg_time_ms': data['total_time'] / data['count'],
                    'min_time_ms': data['min_time'] if data['min_time'] != float('inf') else 0,
                    'max_time_ms': data['max_time'],
                    'total_time_ms': data['total_time'],
                    'slow_queries': data['slow_queries'],
                    'slow_query_rate': (data['slow_queries'] / data['count']) * 100
                }
        return processed_metrics
    
    def reset_metrics(self):
        """Reset all metrics"""
        self.metrics = {}


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


def monitor_performance(operation_name: str = None):
    """
    Decorator to monitor function performance
    
    Args:
        operation_name: Custom name for the operation (defaults to function name)
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            timer_id = performance_monitor.start_timer(op_name)
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = performance_monitor.end_timer(timer_id)
                
                # Log performance info
                if hasattr(g, 'performance_log'):
                    g.performance_log.append({
                        'operation': op_name,
                        'duration_ms': duration,
                        'timestamp': datetime.utcnow().isoformat()
                    })
                else:
                    g.performance_log = [{
                        'operation': op_name,
                        'duration_ms': duration,
                        'timestamp': datetime.utcnow().isoformat()
                    }]
        
        return wrapper
    return decorator


def monitor_api_performance(endpoint_name: str = None):
    """
    Decorator specifically for API endpoints with enhanced monitoring
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            endpoint = endpoint_name or f"{request.method} {request.endpoint}"
            
            # Initialize performance tracking
            g.performance_log = []
            g.api_start_time = start_time
            g.cache_hits = 0
            g.cache_misses = 0
            g.db_queries = 0
            
            try:
                result = func(*args, **kwargs)
                
                # Calculate total response time
                total_time = (time.time() - start_time) * 1000
                
                # Log performance metrics
                performance_data = {
                    'endpoint': endpoint,
                    'method': request.method,
                    'total_time_ms': total_time,
                    'cache_hits': getattr(g, 'cache_hits', 0),
                    'cache_misses': getattr(g, 'cache_misses', 0),
                    'db_queries': getattr(g, 'db_queries', 0),
                    'operations': getattr(g, 'performance_log', []),
                    'timestamp': datetime.utcnow().isoformat(),
                    'user_id': getattr(request, 'user_id', 'anonymous') if hasattr(request, 'user_id') else 'anonymous'
                }
                
                # Store in cache for monitoring dashboard
                cache_key = f"perf_log:{endpoint}:{int(time.time())}"
                cache.set(cache_key, performance_data, ttl=3600)  # Store for 1 hour
                
                # Log slow requests
                if total_time > 1000:  # Slower than 1 second
                    logging.warning(f"Slow API request: {endpoint} took {total_time:.2f}ms")
                
                # Add performance headers to response if it's a Flask response
                if hasattr(result, 'headers'):
                    result.headers['X-Response-Time'] = f"{total_time:.2f}ms"
                    result.headers['X-Cache-Status'] = 'HIT' if getattr(g, 'cache_hits', 0) > 0 else 'MISS'
                
                return result
                
            except Exception as e:
                # Log error with performance context
                error_time = (time.time() - start_time) * 1000
                logging.error(f"API error in {endpoint} after {error_time:.2f}ms: {str(e)}")
                raise
        
        return wrapper
    return decorator


def track_cache_hit():
    """Track a cache hit"""
    if hasattr(g, 'cache_hits'):
        g.cache_hits += 1
    else:
        g.cache_hits = 1


def track_cache_miss():
    """Track a cache miss"""
    if hasattr(g, 'cache_misses'):
        g.cache_misses += 1
    else:
        g.cache_misses = 1


def track_db_query():
    """Track a database query"""
    if hasattr(g, 'db_queries'):
        g.db_queries += 1
    else:
        g.db_queries = 1


def get_performance_summary() -> Dict[str, Any]:
    """Get overall performance summary"""
    metrics = performance_monitor.get_metrics()
    
    # Get recent performance logs from cache
    recent_logs = []
    try:
        # This would need to be implemented to fetch recent logs from cache
        pass
    except Exception as e:
        logging.error(f"Error fetching performance logs: {str(e)}")
    
    return {
        'metrics': metrics,
        'recent_logs': recent_logs,
        'cache_stats': cache.get_stats() if hasattr(cache, 'get_stats') else {},
        'timestamp': datetime.utcnow().isoformat()
    }


def log_performance_warning(message: str, duration_ms: float, context: Dict[str, Any] = None):
    """Log a performance warning with context"""
    warning_data = {
        'message': message,
        'duration_ms': duration_ms,
        'timestamp': datetime.utcnow().isoformat(),
        'context': context or {}
    }
    
    logging.warning(f"Performance Warning: {message} (Duration: {duration_ms:.2f}ms)")
    
    # Store warning in cache for monitoring
    cache_key = f"perf_warning:{int(time.time())}"
    cache.set(cache_key, warning_data, ttl=3600)
