"""
Socket.IO middleware for handling WebSocket connections properly.
This module provides middleware functions to ensure proper WebSocket protocol handling.
"""

import logging
from flask import request
from werkzeug.wrappers import Response

class SocketIOMiddleware:
    """Middleware for handling Socket.IO WebSocket connections properly."""
    
    def __init__(self, app, socketio):
        """Initialize the middleware with the Flask app and Socket.IO instance."""
        self.app = app
        self.socketio = socketio
        self.setup_middleware()
        
    def setup_middleware(self):
        """Set up the middleware to handle WebSocket connections properly."""
        # Register a before_request handler to ensure proper WebSocket protocol handling
        @self.app.before_request
        def handle_websocket_request():
            """Handle WebSocket requests properly."""
            # Check if this is a WebSocket request
            if request.environ.get('HTTP_UPGRADE', '').lower() == 'websocket':
                logging.debug(f"WebSocket request detected: {request.path}")
                
                # Check if this is a Socket.IO WebSocket request
                if '/socket.io/' in request.path:
                    logging.debug(f"Socket.IO WebSocket request detected: {request.path}")
                    
                    # Let Socket.IO handle the WebSocket request
                    return None
            
            # For non-WebSocket requests, continue with normal Flask handling
            return None
            
        # Register an after_request handler to ensure proper WebSocket response headers
        @self.app.after_request
        def handle_websocket_response(response):
            """Handle WebSocket responses properly."""
            # Check if this is a WebSocket response
            if request.environ.get('HTTP_UPGRADE', '').lower() == 'websocket':
                # Ensure proper WebSocket response headers
                if not response.headers.get('Upgrade'):
                    response.headers['Upgrade'] = 'websocket'
                if not response.headers.get('Connection'):
                    response.headers['Connection'] = 'Upgrade'
                    
            return response

def init_socketio_middleware(app, socketio):
    """Initialize Socket.IO middleware for the Flask app."""
    if socketio is not None:
        logging.info("Initializing Socket.IO middleware")
        SocketIOMiddleware(app, socketio)
        logging.info("Socket.IO middleware initialized")
    else:
        logging.warning("Socket.IO instance is None, skipping middleware initialization")