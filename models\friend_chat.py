from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateT<PERSON><PERSON><PERSON>
from models.user import User
from datetime import datetime, timezone
import uuid
import base64
import logging
from utils.encryption import MessageEncryption

# ANSI color codes for terminal output
class TermColors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    RESET = '\033[0m'

class FriendChat(Document):
    """Model for storing chat messages between friends"""
    chat_id = StringField(required=True, unique=True, default=lambda: str(uuid.uuid4()))
    user1 = ReferenceField(User, required=True)
    user2 = ReferenceField(User, required=True)
    messages = ListField(DictField(), default=list)
    created_at = DateTimeField(default=lambda: datetime.now(timezone.utc))
    updated_at = DateTimeField(default=lambda: datetime.now(timezone.utc))

    # End-to-end encryption fields
    e2e_enabled = StringField(default="pending")  # pending, enabled
    user1_public_key = StringField()
    user2_public_key = StringField()
    user1_shared_secret = StringField()
    user2_shared_secret = StringField()

    meta = {
        'collection': 'friend_chats',
        'indexes': [
            'chat_id',
            'user1',
            'user2',
        ]
    }

    @staticmethod
    def encrypt_message(message, chat_id):
        """
        Encrypt a message using strong encryption

        This method uses the MessageEncryption utility to properly encrypt
        the message content with a key derived from the chat_id and a server secret.

        Note: For true end-to-end encryption, the client should encrypt the message
        before sending it to the server using the E2E_* prefix. This server-side
        encryption is a fallback for messages that aren't already E2E encrypted.
        """
        try:
            if not message or not chat_id:
                return message

            # If the message is already E2E encrypted by the client, don't encrypt it again
            if message.startswith('E2E_'):
                return message

            # Use the MessageEncryption utility for proper encryption
            return MessageEncryption.encrypt_message(message, chat_id)
        except Exception as e:
            logging.error(f"Error encrypting message: {str(e)}")
            return message

    @staticmethod
    def decrypt_message(encrypted_message, chat_id):
        """
        Decrypt a message that was encrypted with encrypt_message

        This method handles both the new Fernet encryption and the legacy
        Base64 encoding for backward compatibility.

        Note: For true end-to-end encrypted messages (with E2E_ prefix),
        this method will not attempt to decrypt them as they can only be
        decrypted by the client with the proper keys.
        """
        try:
            if not encrypted_message or not chat_id:
                return encrypted_message

            # If this is an E2E encrypted message, don't try to decrypt it server-side
            if encrypted_message.startswith('E2E_'):
                return "[End-to-end encrypted message - Can only be decrypted by the recipient]"

            # Use the MessageEncryption utility for proper decryption
            # It handles both new Fernet encryption and legacy Base64 encoding
            return MessageEncryption.decrypt_message(encrypted_message, chat_id)
        except Exception as e:
            logging.error(f"Error decrypting message: {str(e)}")
            return f"[Decryption Error]"

    def to_dict(self, limit=20, skip=0):
        """
        Convert to dictionary for JSON serialization

        Args:
            limit (int): Maximum number of messages to return (default: 20)
            skip (int): Number of messages to skip from the end (for pagination, default: 0)
        """
        try:
            other_user1 = {
                'id': str(self.user1.id),
                'username': self.user1.username,
                'display_name': self.user1.display_name or self.user1.username,
                'profile_picture': self.user1.profile_picture
            }
        except Exception as e:
            import logging
            logging.error(f"Error getting user1 data: {str(e)}")
            other_user1 = {
                'id': 'unknown',
                'username': 'Unknown User',
                'display_name': 'Unknown User',
                'profile_picture': None
            }

        try:
            other_user2 = {
                'id': str(self.user2.id),
                'username': self.user2.username,
                'display_name': self.user2.display_name or self.user2.username,
                'profile_picture': self.user2.profile_picture
            }
        except Exception as e:
            import logging
            logging.error(f"Error getting user2 data: {str(e)}")
            other_user2 = {
                'id': 'unknown',
                'username': 'Unknown User',
                'display_name': 'Unknown User',
                'profile_picture': None
            }

        # Get total message count
        total_messages = len(self.messages)

        # Apply pagination - get the latest messages first
        # We reverse the messages list to get newest first, then apply skip and limit
        messages_to_process = list(reversed(self.messages))

        # Apply skip and limit
        if skip >= 0:
            messages_to_process = messages_to_process[skip:skip+limit]

        # Process messages
        processed_messages = []
        for message in messages_to_process:
            processed_message = message.copy()

            # Check if message is encrypted
            if message.get('encrypted', False):
                # Check if this is an E2E encrypted message
                if message.get('e2e_encrypted', False) or (message.get('content', '').startswith('E2E_')):
                    # For E2E encrypted messages, don't attempt to decrypt server-side
                    # The client will handle decryption
                    processed_message['needs_client_decryption'] = True
                    processed_message['e2e_encrypted'] = True
                    # Keep the encrypted content as is
                else:
                    try:
                        # For server-side encrypted messages, decrypt them
                        decrypted_content = FriendChat.decrypt_message(
                            message['content'],
                            self.chat_id
                        )
                        processed_message['content'] = decrypted_content
                        processed_message['encrypted'] = False
                    except Exception as e:
                        import logging
                        logging.error(f"Error decrypting message: {str(e)}")
                        # Keep the encrypted content but mark as having an error
                        processed_message['content'] = "[Encrypted message - Unable to decrypt]"
                        processed_message['decryption_error'] = True

            processed_messages.append(processed_message)

        return {
            'chat_id': self.chat_id,
            'user1': other_user1,
            'user2': other_user2,
            'messages': processed_messages,  # Return processed messages
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'e2e_enabled': self.e2e_enabled,  # Include E2E status
            'total_messages': total_messages,  # Include total message count for pagination
            'has_more': skip + limit < total_messages,  # Flag indicating if there are more messages to load
            'timezone': 'UTC'  # Indicate that timestamps are in UTC for client-side conversion
        }

    def add_message(self, sender, content, images=None):
        """Add a message to the chat"""
        # Import logging at the method level to ensure it's available in the exception handler
        import logging
        
        try:
            # Always store timestamps in UTC for consistency
            timestamp = datetime.now(timezone.utc)

            # Check if this is an already E2E encrypted message from the client
            is_e2e_encrypted = content.startswith('E2E_') if content else False

            # For E2E encrypted messages, store as is
            # For regular messages, use server-side encryption
            if is_e2e_encrypted:
                encrypted_content = content  # Already encrypted by the client
            else:
                # Only use server-side encryption if E2E is not enabled and there's content
                if content and self.e2e_enabled == "enabled":
                    # If E2E is enabled but the message isn't E2E encrypted,
                    # we should warn the client but still encrypt it server-side
                    logging.warning(f"Received non-E2E encrypted message for chat {self.chat_id} with E2E enabled")

                # Fallback to server-side encryption if there's content
                encrypted_content = FriendChat.encrypt_message(content, self.chat_id) if content else ""

            message = {
                "user_id": str(sender.id),
                "username": sender.username,
                "display_name": sender.display_name or sender.username,
                "content": encrypted_content,  # Store encrypted content
                "timestamp": timestamp.isoformat(),  # Store as ISO format for client-side timezone conversion
                "encrypted": True if content else False,  # Flag to indicate this message is encrypted
                "e2e_encrypted": is_e2e_encrypted  # Flag to indicate if this is E2E encrypted
            }

            # Add images if provided
            if images and isinstance(images, list) and len(images) > 0:
                message["images"] = images
                # Use colored logging for image uploads
                print(f"{TermColors.RED}[FRIEND CHAT IMAGE] Adding {len(images)} images to message in chat {self.chat_id}{TermColors.RESET}")
                logging.info(f"Adding {len(images)} images to message in chat {self.chat_id}")

            self.messages.append(message)
            self.updated_at = timestamp
            self.save()

            # Return decrypted message for immediate use
            decrypted_message = message.copy()

            # For E2E encrypted messages, the client will need to decrypt it
            # For server-encrypted messages, we can return the original content
            if content:
                if is_e2e_encrypted:
                    # Keep the encrypted content - client will decrypt it
                    decrypted_message["needs_client_decryption"] = True
                else:
                    # Use original content for immediate display
                    decrypted_message["content"] = content
                    decrypted_message["encrypted"] = False

            return decrypted_message
        except Exception as e:
            # No need to import logging here since we imported it at the method level
            error_msg = f"Error adding message: {str(e)}"
            logging.error(error_msg)
            
            # Use colored logging for errors
            print(f"{TermColors.RED}[FRIEND CHAT ERROR] {error_msg}{TermColors.RESET}")
            
            # Log the full traceback for better debugging
            import traceback
            traceback_str = traceback.format_exc()
            logging.error(f"Traceback: {traceback_str}")
            print(f"{TermColors.RED}[FRIEND CHAT ERROR] Traceback: {traceback_str}{TermColors.RESET}")
            
            # Return a basic message object with error info
            return {
                "user_id": str(sender.id) if sender else "unknown",
                "username": "Unknown User",
                "display_name": "Unknown User",
                "content": "Error sending message. Please try again.",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "error": str(e),
                "encrypted": False
            }

    @classmethod
    def get_or_create_chat(cls, user1_id, user2_id):
        """Get or create a chat between two users"""
        try:
            # Check both directions
            chat = cls.objects(user1=user1_id, user2=user2_id).first() or \
                cls.objects(user1=user2_id, user2=user1_id).first()

            if not chat:
                # Create new chat
                chat = cls(
                    user1=user1_id,
                    user2=user2_id,
                )
                chat.save()

            return chat
        except Exception as e:
            import logging
            logging.error(f"Error getting or creating chat: {str(e)}")
            # Try to recover by creating a new chat
            try:
                from models.user import User
                # Verify users exist
                user1 = User.objects(id=user1_id).first()
                user2 = User.objects(id=user2_id).first()

                if not user1 or not user2:
                    logging.error(f"Cannot create chat: User(s) not found. user1_id={user1_id}, user2_id={user2_id}")
                    return None

                # Create new chat
                chat = cls(
                    user1=user1,
                    user2=user2,
                )
                chat.save()
                return chat
            except Exception as inner_e:
                logging.error(f"Failed to recover from error: {str(inner_e)}")
                return None

    @classmethod
    def get_chats_for_user(cls, user_id):
        """Get all chats for a user"""
        return list(cls.objects(user1=user_id)) + list(cls.objects(user2=user_id))

    @classmethod
    def cleanup_stale_chats(cls):
        """Clean up chats with missing users"""
        import logging
        from models.user import User

        try:
            # Get all chats
            all_chats = cls.objects()
            stale_chats = []

            for chat in all_chats:
                try:
                    # Check if both users exist
                    user1_exists = User.objects(id=chat.user1.id).first() is not None
                    user2_exists = User.objects(id=chat.user2.id).first() is not None

                    if not user1_exists or not user2_exists:
                        stale_chats.append(chat)
                except Exception as e:
                    # If we can't access user1 or user2, the chat is stale
                    logging.error(f"Error checking chat {chat.chat_id}: {str(e)}")
                    stale_chats.append(chat)

            # Delete stale chats
            for chat in stale_chats:
                logging.info(f"Deleting stale chat {chat.chat_id}")
                chat.delete()

            return len(stale_chats)
        except Exception as e:
            logging.error(f"Error cleaning up stale chats: {str(e)}")
            return 0

    def is_member(self, user):
        """Check if a user is a member of this chat"""
        try:
            user1_id = str(self.user1.id) if self.user1 else None
            user2_id = str(self.user2.id) if self.user2 else None
            return str(user.id) == user1_id or str(user.id) == user2_id
        except Exception as e:
            # Handle case where referenced users don't exist
            import logging
            logging.error(f"Error checking chat membership: {str(e)}")
            return False

    def get_other_user(self, user):
        """Get the other user in the chat"""
        try:
            if str(user.id) == str(self.user1.id):
                return self.user2
            elif str(user.id) == str(self.user2.id):
                return self.user1
            return None
        except Exception as e:
            # Handle case where referenced users don't exist
            import logging
            logging.error(f"Error getting other user: {str(e)}")
            return None

    def store_public_key(self, user, public_key):
        """Store a user's public key for E2E encryption"""
        try:
            if str(user.id) == str(self.user1.id):
                self.user1_public_key = public_key
            elif str(user.id) == str(self.user2.id):
                self.user2_public_key = public_key
            else:
                logging.error(f"User {user.username} is not a member of chat {self.chat_id}")
                return False

            self.save()
            return True
        except Exception as e:
            logging.error(f"Error storing public key: {str(e)}")
            return False

    def get_public_key(self, user):
        """Get a user's public key"""
        try:
            if str(user.id) == str(self.user1.id):
                return self.user1_public_key
            elif str(user.id) == str(self.user2.id):
                return self.user2_public_key
            return None
        except Exception as e:
            logging.error(f"Error getting public key: {str(e)}")
            return None

    def get_other_user_public_key(self, user):
        """Get the other user's public key"""
        try:
            if str(user.id) == str(self.user1.id):
                return self.user2_public_key
            elif str(user.id) == str(self.user2.id):
                return self.user1_public_key
            return None
        except Exception as e:
            logging.error(f"Error getting other user's public key: {str(e)}")
            return None

    def is_e2e_ready(self):
        """Check if E2E encryption is ready"""
        return (
            self.e2e_enabled == "enabled" and
            self.user1_public_key is not None and
            self.user2_public_key is not None and
            self.user1_shared_secret is not None and
            self.user2_shared_secret is not None
        )
