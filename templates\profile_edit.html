{% extends "base.html" %}

{% block title %}Edit Profile{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500&family=Outfit:wght@400;500;600;700;800&family=Space+Grotesk:wght@400;500;600;700&display=swap" rel="stylesheet">

<!-- Custom CSS -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/profile-edit.css') }}">
{% endblock %}

{% block content %}
<div class="edit-container">
    <div class="edit-header">
        <h1 class="edit-title">Customize Your Profile</h1>
        <p class="edit-subtitle">Make your profile uniquely yours with these customization options</p>
    </div>
    
    <form id="profile-edit-form" class="edit-form">
        <!-- Profile Info Section -->
        <div class="edit-section profile-info-section">
            <div class="profile-info-header">
                <div class="profile-picture-edit">
                    <img src="{{ user.profile_picture or url_for('static', filename='images/default-avatar.png') }}" 
                         alt="{{ user.username }}" crossorigin="anonymous">
                    <div class="profile-picture-overlay">
                        <span class="profile-picture-icon"><i class="fas fa-camera"></i></span>
                    </div>
                    <input type="file" id="profile-picture-input" accept="image/*" style="display: none;">
                </div>
                
                <div class="profile-info-details">
                    <h2 class="profile-info-name">{{ user.display_name or user.username }}</h2>
                    <p class="profile-info-username">@{{ user.username }}</p>
                </div>
            </div>
            
            <div class="form-group">
                <label for="display_name" class="form-label">Display Name</label>
                <p class="form-hint">This is how your name will appear on your profile</p>
                <input type="text" id="display_name" name="display_name" class="form-input" 
                       value="{{ user.display_name or '' }}" placeholder="Your display name">
            </div>
            
            <input type="hidden" name="username" value="{{ user.username }}">
        </div>
        
        <!-- Appearance Section -->
        <div class="edit-section">
            <h3 class="edit-section-title">Appearance</h3>
            
            <div class="form-group">
                <label for="layout_type" class="form-label">Layout Style</label>
                <select id="layout_type" name="layout_type" class="form-select">
                    <option value="standard" {% if profile.layout_type == 'standard' %}selected{% endif %}>Standard</option>
                    <option value="compact" {% if profile.layout_type == 'compact' %}selected{% endif %}>Compact</option>
                    <option value="expanded" {% if profile.layout_type == 'expanded' %}selected{% endif %}>Expanded</option>
                    <option value="minimal" {% if profile.layout_type == 'minimal' %}selected{% endif %}>Minimal</option>
                </select>
            </div>
            
            <!-- Profile Section Colors -->
            <h4 class="edit-subsection-title">Profile Colors</h4>
            
            <div class="color-preview-container">
                <div id="background-preview" class="background-preview"></div>
                <p class="form-hint">Preview of your selected colors</p>
                <button type="button" id="reset-colors-button" class="secondary-button">
                    <i class="fas fa-undo"></i> Reset Colors to Default
                </button>
            </div>
            
            <div class="form-group">
                <label for="profile_container_color" class="form-label">Profile Container Color</label>
                <div class="color-picker-wrapper">
                    <div class="color-preview" style="background-color: {{ profile.profile_container_color or '#ffffff' }};"></div>
                    <input type="color" id="profile_container_color" name="profile_container_color" 
                           value="{{ profile.profile_container_color or '#ffffff' }}" class="form-input">
                </div>
                <p class="form-hint">The main background color of your profile</p>
            </div>
            
            <div class="form-group">
                <label for="profile_header_color" class="form-label">Profile Header Color</label>
                <div class="color-picker-wrapper">
                    <div class="color-preview" style="background-color: {{ profile.profile_header_color or '#f8f9fa' }};"></div>
                    <input type="color" id="profile_header_color" name="profile_header_color" 
                           value="{{ profile.profile_header_color or '#f8f9fa' }}" class="form-input">
                </div>
                <p class="form-hint">The color of your profile header section</p>
            </div>
            
            <div class="form-group">
                <label for="profile_about_color" class="form-label">About Section Color</label>
                <div class="color-picker-wrapper">
                    <div class="color-preview" style="background-color: {{ profile.profile_about_color or '#f8f9fa' }};"></div>
                    <input type="color" id="profile_about_color" name="profile_about_color" 
                           value="{{ profile.profile_about_color or '#f8f9fa' }}" class="form-input">
                </div>
                <p class="form-hint">The background color of your about section</p>
            </div>
            
            <div class="form-group">
                <label for="profile_content_color" class="form-label">Content Section Color</label>
                <div class="color-picker-wrapper">
                    <div class="color-preview" style="background-color: {{ profile.profile_content_color or '#ffffff' }};"></div>
                    <input type="color" id="profile_content_color" name="profile_content_color" 
                           value="{{ profile.profile_content_color or '#ffffff' }}" class="form-input">
                </div>
                <p class="form-hint">The background color of your content sections</p>
            </div>
            
            <div class="form-group">
                <label for="profile_friends_color" class="form-label">Friends Section Color</label>
                <div class="color-picker-wrapper">
                    <div class="color-preview" style="background-color: {{ profile.profile_friends_color or '#f8f9fa' }};"></div>
                    <input type="color" id="profile_friends_color" name="profile_friends_color" 
                           value="{{ profile.profile_friends_color or '#f8f9fa' }}" class="form-input">
                </div>
                <p class="form-hint">The background color of your friends section</p>
            </div>
            
            <div class="form-group">
                <label for="text_color" class="form-label">Text Color</label>
                <div class="color-picker-wrapper">
                    <div class="color-preview" style="background-color: {{ profile.text_color }};"></div>
                    <input type="color" id="text_color" name="text_color" 
                           value="{{ profile.text_color }}" class="form-input">
                </div>
                <p class="form-hint">The color of text throughout your profile</p>
            </div>
            
            <div class="form-group">
                <label for="accent_color" class="form-label">Accent Color</label>
                <div class="color-picker-wrapper">
                    <div class="color-preview" style="background-color: {{ profile.accent_color }};"></div>
                    <input type="color" id="accent_color" name="accent_color" 
                           value="{{ profile.accent_color }}" class="form-input">
                </div>
                <p class="form-hint">The color for buttons, links, and highlights</p>
            </div>
        </div>
        
        <!-- Typography Section -->
        <div class="edit-section">
            <h3 class="edit-section-title">Typography</h3>
            
            <div class="form-group">
                <label for="font_family" class="form-label">Main Font</label>
                <select id="font_family" name="font_family" class="form-select">
                    <option value="Inter, system-ui, sans-serif" {% if 'Inter' in profile.font_family %}selected{% endif %}>Inter</option>
                    <option value="'Space Grotesk', system-ui, sans-serif" {% if 'Space Grotesk' in profile.font_family %}selected{% endif %}>Space Grotesk</option>
                    <option value="'Outfit', system-ui, sans-serif" {% if 'Outfit' in profile.font_family %}selected{% endif %}>Outfit</option>
                    <option value="'Poppins', system-ui, sans-serif" {% if 'Poppins' in profile.font_family %}selected{% endif %}>Poppins</option>
                    <option value="'Montserrat', system-ui, sans-serif" {% if 'Montserrat' in profile.font_family %}selected{% endif %}>Montserrat</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="heading_font" class="form-label">Heading Font</label>
                <select id="heading_font" name="heading_font" class="form-select">
                    <option value="'Space Grotesk', system-ui, sans-serif" {% if 'Space Grotesk' in profile.heading_font %}selected{% endif %}>Space Grotesk</option>
                    <option value="'Outfit', system-ui, sans-serif" {% if 'Outfit' in profile.heading_font %}selected{% endif %}>Outfit</option>
                    <option value="Inter, system-ui, sans-serif" {% if 'Inter' in profile.heading_font %}selected{% endif %}>Inter</option>
                    <option value="'Poppins', system-ui, sans-serif" {% if 'Poppins' in profile.heading_font %}selected{% endif %}>Poppins</option>
                    <option value="'Montserrat', system-ui, sans-serif" {% if 'Montserrat' in profile.heading_font %}selected{% endif %}>Montserrat</option>
                </select>
            </div>
            

        </div>
        
        <!-- Content Display Section -->
        <div class="edit-section">
            <h3 class="edit-section-title">Content Display</h3>
            
            <div class="form-group">
                <label class="form-label">
                    <input type="checkbox" class="form-checkbox" name="show_activity" 
                           {% if profile.show_activity %}checked{% endif %}>
                    Show Activity
                </label>
                <p class="form-hint">Display your recent activity on your profile</p>
            </div>
            
            <div class="form-group">
                <label class="form-label">
                    <input type="checkbox" class="form-checkbox" name="show_friends" 
                           {% if profile.show_friends %}checked{% endif %}>
                    Show Friends
                </label>
                <p class="form-hint">Display your friends list on your profile</p>
            </div>
            
            <div class="form-group">
                <label class="form-label">
                    <input type="checkbox" class="form-checkbox" name="show_stats" 
                           {% if profile.show_stats %}checked{% endif %}>
                    Show Statistics
                </label>
                <p class="form-hint">Display usage statistics on your profile</p>
            </div>
            
            <div class="form-group">
                <label class="form-label">
                    <input type="checkbox" class="form-checkbox" name="is_public" 
                           {% if profile.is_public %}checked{% endif %}>
                    Public Profile
                </label>
                <p class="form-hint">Make your profile visible to everyone</p>
            </div>
        </div>
        
        <!-- Social Links Section -->
        <div class="edit-section">
            <h3 class="edit-section-title">Social Links</h3>
            
            <div id="social-links-container" class="social-links-container">
                {% if profile.social_links %}
                    {% for platform, url in profile.social_links.items() %}
                    <div class="social-link-item">
                        <div class="social-link-icon">
                            {% set platform_lower = platform.lower() %}
                            {% if platform_lower == 'x' or platform_lower == 'twitter' %}
                                <i class="fab fa-x-twitter"></i>
                            {% elif platform_lower == 'instagram' %}
                                <i class="fab fa-instagram"></i>
                            {% elif platform_lower == 'facebook' %}
                                <i class="fab fa-facebook"></i>
                            {% elif platform_lower == 'linkedin' %}
                                <i class="fab fa-linkedin"></i>
                            {% elif platform_lower == 'github' %}
                                <i class="fab fa-github"></i>
                            {% elif platform_lower == 'youtube' %}
                                <i class="fab fa-youtube"></i>
                            {% elif platform_lower == 'twitch' %}
                                <i class="fab fa-twitch"></i>
                            {% elif platform_lower == 'discord' %}
                                <i class="fab fa-discord"></i>
                            {% elif platform_lower == 'tiktok' %}
                                <i class="fab fa-tiktok"></i>
                            {% elif platform_lower == 'reddit' %}
                                <i class="fab fa-reddit"></i>
                            {% else %}
                                <i class="fas fa-link"></i>
                            {% endif %}
                        </div>
                        <select class="form-input social-link-platform" name="social_platform[]">
                            <option value="Twitter" {% if platform_lower == 'twitter' or platform_lower == 'x' %}selected{% endif %}>Twitter</option>
                            <option value="Instagram" {% if platform_lower == 'instagram' %}selected{% endif %}>Instagram</option>
                            <option value="Facebook" {% if platform_lower == 'facebook' %}selected{% endif %}>Facebook</option>
                            <option value="LinkedIn" {% if platform_lower == 'linkedin' %}selected{% endif %}>LinkedIn</option>
                            <option value="GitHub" {% if platform_lower == 'github' %}selected{% endif %}>GitHub</option>
                            <option value="YouTube" {% if platform_lower == 'youtube' %}selected{% endif %}>YouTube</option>
                            <option value="Twitch" {% if platform_lower == 'twitch' %}selected{% endif %}>Twitch</option>
                            <option value="Discord" {% if platform_lower == 'discord' %}selected{% endif %}>Discord</option>
                            <option value="TikTok" {% if platform_lower == 'tiktok' %}selected{% endif %}>TikTok</option>
                            <option value="Reddit" {% if platform_lower == 'reddit' %}selected{% endif %}>Reddit</option>
                            <option value="Other" {% if platform_lower not in ['twitter', 'x', 'instagram', 'facebook', 'linkedin', 'github', 'youtube', 'twitch', 'discord', 'tiktok', 'reddit'] %}selected{% endif %}>Other</option>
                        </select>
                        <input type="url" class="form-input social-link-url" name="social_url[]" value="{{ url }}" placeholder="https://">
                        <button type="button" class="social-link-remove" aria-label="Remove social link">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    {% endfor %}
                {% endif %}
            </div>
            
            <button type="button" id="add-social-link" class="add-social-link">
                <i class="fas fa-plus"></i> Add Social Link
            </button>
        </div>
        
        <!-- About Me Section -->
        <div class="edit-section">
            <h3 class="edit-section-title">About Me</h3>
            <p class="form-hint">Tell others about yourself, your interests, skills, or anything else</p>
            
            <div class="form-group">
                <textarea id="about_me_content" class="form-textarea" name="about_me_content" 
                          placeholder="About me content (HTML supported)" rows="6">{{ profile.custom_sections.about if profile.custom_sections and profile.custom_sections.about else '' }}</textarea>
            </div>
        </div>
        
        <!-- Preview Section -->
        <div class="edit-section">
            <h3 class="edit-section-title">Preview</h3>
            <p class="form-hint">See how your profile will look with the current settings</p>
            
            <button type="button" id="preview-button" class="btn btn-secondary">
                <i class="fas fa-eye"></i> Generate Preview
            </button>
            
            <iframe id="preview-frame" class="preview-frame" src="{{ url_for('profile_page.preview_profile') }}"></iframe>
        </div>
        
        <!-- Form Actions -->
        <div class="form-actions">
            <a href="{{ url_for('profile_page.view_profile', username=user.username) }}" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Save Changes
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/profile-edit.js') }}"></script>
{% endblock %}