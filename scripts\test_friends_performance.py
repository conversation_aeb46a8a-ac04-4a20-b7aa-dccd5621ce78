"""
Performance testing script for Friends API optimizations
Tests the optimized endpoints and measures performance improvements
"""

import sys
import os
import time
import requests
import json
from datetime import datetime

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def test_api_performance(base_url="http://localhost:2000", test_user_credentials=None):
    """
    Test the performance of optimized Friends API endpoints
    
    Args:
        base_url: Base URL of the application
        test_user_credentials: Dict with 'username' and 'password' for testing
    """
    
    print("=== Friends API Performance Testing ===")
    print(f"Testing against: {base_url}")
    print(f"Started at: {datetime.now()}")
    print()
    
    # Test results storage
    results = {
        'timestamp': datetime.now().isoformat(),
        'base_url': base_url,
        'tests': {}
    }
    
    session = requests.Session()
    
    # If credentials provided, attempt login
    if test_user_credentials:
        try:
            login_response = session.post(
                f"{base_url}/api/auth/login",
                json=test_user_credentials,
                timeout=10
            )
            if login_response.status_code == 200:
                print("✓ Successfully logged in for testing")
            else:
                print(f"⚠ Login failed: {login_response.status_code}")
                print("Continuing with anonymous testing...")
        except Exception as e:
            print(f"⚠ Login error: {str(e)}")
            print("Continuing with anonymous testing...")
    
    # Test 1: Friends List API Performance
    print("1. Testing Friends List API Performance...")
    friends_times = []
    
    for i in range(5):  # Run 5 tests
        try:
            start_time = time.time()
            response = session.get(f"{base_url}/api/friends/friends", timeout=10)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # Convert to milliseconds
            friends_times.append(response_time)
            
            if response.status_code == 200:
                data = response.json()
                cached = data.get('cached', False)
                reported_time = data.get('response_time_ms', 'N/A')
                friends_count = data.get('count', 0)
                
                print(f"   Test {i+1}: {response_time:.2f}ms (Cached: {cached}, Reported: {reported_time}ms, Friends: {friends_count})")
            else:
                print(f"   Test {i+1}: {response_time:.2f}ms (Status: {response.status_code})")
                
        except Exception as e:
            print(f"   Test {i+1}: Error - {str(e)}")
            friends_times.append(10000)  # 10 second penalty for errors
    
    # Calculate friends list statistics
    if friends_times:
        results['tests']['friends_list'] = {
            'avg_time_ms': sum(friends_times) / len(friends_times),
            'min_time_ms': min(friends_times),
            'max_time_ms': max(friends_times),
            'times': friends_times,
            'target_met': all(t < 500 for t in friends_times)  # Target: <500ms
        }
        
        avg_time = results['tests']['friends_list']['avg_time_ms']
        target_met = results['tests']['friends_list']['target_met']
        status = "✓ PASS" if target_met else "✗ FAIL"
        print(f"   Average: {avg_time:.2f}ms {status}")
    
    print()
    
    # Test 2: Chat Messages API Performance (if we have a chat ID)
    print("2. Testing Chat Messages API Performance...")
    
    # Try to get a chat ID from friends list
    chat_id = None
    try:
        response = session.get(f"{base_url}/api/friends/friends", timeout=10)
        if response.status_code == 200:
            data = response.json()
            friends = data.get('friends', [])
            if friends and len(friends) > 0:
                chat_id = friends[0].get('chat_id')
    except Exception as e:
        print(f"   Could not get chat ID: {str(e)}")
    
    if chat_id:
        chat_times = []
        
        for i in range(5):  # Run 5 tests
            try:
                start_time = time.time()
                response = session.get(
                    f"{base_url}/api/friends/chat/{chat_id}/messages?limit=20&skip=0",
                    timeout=10
                )
                end_time = time.time()
                
                response_time = (end_time - start_time) * 1000  # Convert to milliseconds
                chat_times.append(response_time)
                
                if response.status_code == 200:
                    data = response.json()
                    cached = data.get('cached', False)
                    reported_time = data.get('response_time_ms', 'N/A')
                    message_count = len(data.get('messages', []))
                    
                    print(f"   Test {i+1}: {response_time:.2f}ms (Cached: {cached}, Reported: {reported_time}ms, Messages: {message_count})")
                else:
                    print(f"   Test {i+1}: {response_time:.2f}ms (Status: {response.status_code})")
                    
            except Exception as e:
                print(f"   Test {i+1}: Error - {str(e)}")
                chat_times.append(10000)  # 10 second penalty for errors
        
        # Calculate chat messages statistics
        if chat_times:
            results['tests']['chat_messages'] = {
                'avg_time_ms': sum(chat_times) / len(chat_times),
                'min_time_ms': min(chat_times),
                'max_time_ms': max(chat_times),
                'times': chat_times,
                'target_met': all(t < 500 for t in chat_times),  # Target: <500ms
                'chat_id': chat_id
            }
            
            avg_time = results['tests']['chat_messages']['avg_time_ms']
            target_met = results['tests']['chat_messages']['target_met']
            status = "✓ PASS" if target_met else "✗ FAIL"
            print(f"   Average: {avg_time:.2f}ms {status}")
    else:
        print("   Skipped - No chat ID available for testing")
        results['tests']['chat_messages'] = {'skipped': True, 'reason': 'No chat ID available'}
    
    print()
    
    # Test 3: Cache Performance Test
    print("3. Testing Cache Performance...")
    
    if chat_id:
        try:
            # First request (should be cache miss)
            start_time = time.time()
            response1 = session.get(f"{base_url}/api/friends/chat/{chat_id}/messages?limit=20&skip=0", timeout=10)
            time1 = (time.time() - start_time) * 1000
            
            # Second request immediately (should be cache hit)
            start_time = time.time()
            response2 = session.get(f"{base_url}/api/friends/chat/{chat_id}/messages?limit=20&skip=0", timeout=10)
            time2 = (time.time() - start_time) * 1000
            
            if response1.status_code == 200 and response2.status_code == 200:
                data1 = response1.json()
                data2 = response2.json()
                
                cached1 = data1.get('cached', False)
                cached2 = data2.get('cached', False)
                
                print(f"   First request: {time1:.2f}ms (Cached: {cached1})")
                print(f"   Second request: {time2:.2f}ms (Cached: {cached2})")
                
                if cached2 and time2 < time1:
                    print("   ✓ Cache is working - second request was faster and cached")
                    cache_working = True
                else:
                    print("   ⚠ Cache may not be working optimally")
                    cache_working = False
                
                results['tests']['cache_performance'] = {
                    'first_request_ms': time1,
                    'second_request_ms': time2,
                    'cache_working': cache_working,
                    'improvement_percent': ((time1 - time2) / time1) * 100 if time1 > 0 else 0
                }
            else:
                print("   Error testing cache performance")
                results['tests']['cache_performance'] = {'error': 'HTTP error'}
                
        except Exception as e:
            print(f"   Error testing cache: {str(e)}")
            results['tests']['cache_performance'] = {'error': str(e)}
    else:
        print("   Skipped - No chat ID available for cache testing")
        results['tests']['cache_performance'] = {'skipped': True}
    
    print()
    
    # Test 4: Load Test (Simple)
    print("4. Simple Load Test (10 concurrent-ish requests)...")
    
    load_times = []
    start_time = time.time()
    
    # Simulate some load by making multiple requests quickly
    for i in range(10):
        try:
            req_start = time.time()
            response = session.get(f"{base_url}/api/friends/friends", timeout=10)
            req_time = (time.time() - req_start) * 1000
            load_times.append(req_time)
            
            if response.status_code == 200:
                print(f"   Request {i+1}: {req_time:.2f}ms")
            else:
                print(f"   Request {i+1}: {req_time:.2f}ms (Status: {response.status_code})")
                
        except Exception as e:
            print(f"   Request {i+1}: Error - {str(e)}")
            load_times.append(10000)
    
    total_load_time = (time.time() - start_time) * 1000
    
    if load_times:
        results['tests']['load_test'] = {
            'total_time_ms': total_load_time,
            'avg_request_time_ms': sum(load_times) / len(load_times),
            'min_time_ms': min(load_times),
            'max_time_ms': max(load_times),
            'requests_per_second': 10 / (total_load_time / 1000) if total_load_time > 0 else 0,
            'all_requests_under_1s': all(t < 1000 for t in load_times)
        }
        
        avg_time = results['tests']['load_test']['avg_request_time_ms']
        rps = results['tests']['load_test']['requests_per_second']
        print(f"   Load test completed: {avg_time:.2f}ms average, {rps:.2f} req/sec")
    
    print()
    
    # Summary
    print("=== Performance Test Summary ===")
    
    overall_pass = True
    
    for test_name, test_data in results['tests'].items():
        if test_data.get('skipped'):
            print(f"{test_name}: SKIPPED - {test_data.get('reason', 'Unknown')}")
        elif test_data.get('error'):
            print(f"{test_name}: ERROR - {test_data['error']}")
            overall_pass = False
        else:
            if test_name == 'friends_list':
                status = "PASS" if test_data.get('target_met', False) else "FAIL"
                print(f"{test_name}: {status} - {test_data['avg_time_ms']:.2f}ms average")
                if not test_data.get('target_met', False):
                    overall_pass = False
            elif test_name == 'chat_messages':
                status = "PASS" if test_data.get('target_met', False) else "FAIL"
                print(f"{test_name}: {status} - {test_data['avg_time_ms']:.2f}ms average")
                if not test_data.get('target_met', False):
                    overall_pass = False
            elif test_name == 'cache_performance':
                status = "PASS" if test_data.get('cache_working', False) else "FAIL"
                improvement = test_data.get('improvement_percent', 0)
                print(f"{test_name}: {status} - {improvement:.1f}% improvement")
                if not test_data.get('cache_working', False):
                    overall_pass = False
            elif test_name == 'load_test':
                status = "PASS" if test_data.get('all_requests_under_1s', False) else "FAIL"
                rps = test_data.get('requests_per_second', 0)
                print(f"{test_name}: {status} - {rps:.2f} requests/second")
                if not test_data.get('all_requests_under_1s', False):
                    overall_pass = False
    
    print()
    print(f"Overall Result: {'✓ ALL TESTS PASSED' if overall_pass else '✗ SOME TESTS FAILED'}")
    print(f"Completed at: {datetime.now()}")
    
    # Save results to file
    results_file = f"performance_test_results_{int(time.time())}.json"
    try:
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"Results saved to: {results_file}")
    except Exception as e:
        print(f"Could not save results: {str(e)}")
    
    return results


if __name__ == "__main__":
    # You can modify these for your testing environment
    BASE_URL = "http://localhost:2000"
    
    # Optional: Add test user credentials
    # TEST_CREDENTIALS = {
    #     "username": "testuser",
    #     "password": "testpassword"
    # }
    TEST_CREDENTIALS = None
    
    # Run the performance tests
    test_results = test_api_performance(BASE_URL, TEST_CREDENTIALS)
