"""
Friend chat routes.
This module contains routes for managing direct chats between friends.
"""
from flask import jsonify, request, current_app
from flask_login import login_required, current_user
from .. import friends_api
from models.friend_chat import FriendChat
from utils.decorators import check_service_access
from utils.api_logger import log_api_request
from utils.redis_cache import cache, cached, invalidate_chat_cache
from utils.performance_monitor import monitor_api_performance, track_cache_hit, track_cache_miss, track_db_query
from datetime import datetime, timezone
import logging
import time


@friends_api.route('/chat/<chat_id>', methods=['GET'])
@login_required
@check_service_access('friends')
def get_chat(chat_id):
    """Get a chat by ID with pagination support"""
    chat = FriendChat.objects(chat_id=chat_id).first()

    if not chat:
        return jsonify({'error': 'Chat not found'}), 404

    # Check if user is allowed to view this chat
    if not chat.is_member(current_user):
        return jsonify({'error': 'Not authorized to view this chat'}), 403

    # Get pagination parameters
    limit = request.args.get('limit', 20, type=int)
    skip = request.args.get('skip', 0, type=int)

    # Limit the number of messages to prevent excessive load
    if limit > 50:
        limit = 50

    return jsonify(chat.to_dict(limit=limit, skip=skip))


@friends_api.route('/chat/<chat_id>/messages', methods=['POST'])
@login_required
@check_service_access('friends')
@log_api_request('friend_message', 'friends')
def send_message(chat_id):
    """Send a message to a chat"""
    data = request.json
    message = data.get('message', '')
    images = data.get('images', [])
    is_system = data.get('is_system', False)
    message_type = data.get('message_type', 'info')

    # Require either a message or images
    if not message.strip() and not images:
        return jsonify({'error': 'Message or images are required'}), 400

    # Get the chat
    chat = FriendChat.objects(chat_id=chat_id).first()
    if not chat:
        return jsonify({'error': 'Chat not found'}), 404

    # Check if user is allowed to send messages to this chat
    if not chat.is_member(current_user):
        return jsonify({'error': 'Not authorized to send messages to this chat'}), 403

    # Handle system messages
    if is_system:
        from datetime import datetime, timezone

        # System messages are not end-to-end encrypted
        # They are visible to all users and the server
        timestamp = datetime.now(timezone.utc)
        message_obj = {
            "user_id": "system",
            "username": "System",
            "display_name": "System",
            "content": message,  # Store plaintext for system messages
            "timestamp": timestamp.isoformat(),
            "is_system": True,
            "message_type": message_type,
            "encrypted": False  # System messages are not encrypted
        }

        # For invitation messages, include room_id and room_url
        if message_type == 'invite':
            room_id = data.get('room_id')
            room_url = data.get('room_url')

            if room_id:
                message_obj["room_id"] = room_id

            if room_url:
                message_obj["room_url"] = room_url
            elif room_id:
                # Construct room URL if not provided but room_id is available
                base_url = request.host_url.rstrip('/')
                message_obj["room_url"] = f"{base_url}/live/room/{room_id}"

            logging.info(f"Added room_id and room_url to invitation message: {room_id}, {message_obj.get('room_url')}")

        # Add to chat
        chat.messages.append(message_obj)
        chat.updated_at = timestamp
        chat.save()
    else:
        # Add regular message to chat
        # The message should already be encrypted by the client if E2E is enabled
        message_obj = chat.add_message(current_user, message, images)

    # Invalidate cache for this chat when new message is added
    invalidate_chat_cache(chat_id)

    return jsonify({
        'success': True,
        'message': message_obj
    })


@friends_api.route('/chat/<chat_id>/messages', methods=['GET'])
@login_required
@check_service_access('friends')
@monitor_api_performance('get_chat_messages')
def get_chat_messages(chat_id):
    """Get messages for a chat with ultra-fast caching and optimized pagination"""
    start_time = time.time()

    # Get pagination parameters
    limit = request.args.get('limit', 20, type=int)
    skip = request.args.get('skip', 0, type=int)

    # Limit the number of messages to prevent excessive load
    if limit > 50:
        limit = 50

    # Generate cache key for this specific request
    cache_key = f"messages:{chat_id}:{current_user.id}:{limit}:{skip}"

    # Try to get from cache first (5-minute cache for ultra-fast performance)
    cached_result = cache.get(cache_key)
    if cached_result is not None:
        track_cache_hit()
        # Add performance metrics to cached response
        cached_result['cached'] = True
        cached_result['response_time_ms'] = round((time.time() - start_time) * 1000, 2)
        return jsonify(cached_result)

    # Cache miss - fetch from database
    track_cache_miss()
    track_db_query()
    chat = FriendChat.objects(chat_id=chat_id).first()

    if not chat:
        return jsonify({'error': 'Chat not found'}), 404

    # Check if user is allowed to view this chat
    if not chat.is_member(current_user):
        return jsonify({'error': 'Not authorized to view this chat'}), 403

    # Get optimized chat data with pagination
    chat_data = get_optimized_chat_messages(chat, limit, skip, current_user.id)

    # Cache the result for 5 minutes (300 seconds)
    cache.set(cache_key, chat_data, ttl=300)

    # Add performance metrics
    chat_data['cached'] = False
    chat_data['response_time_ms'] = round((time.time() - start_time) * 1000, 2)

    return jsonify(chat_data)


def get_optimized_chat_messages(chat, limit, skip, user_id):
    """
    Optimized message retrieval with minimal processing overhead
    """
    # Get total message count efficiently
    total_messages = len(chat.messages)

    # Calculate pagination efficiently
    if skip >= total_messages:
        return {
            'messages': [],
            'total_messages': total_messages,
            'has_more': False
        }

    # Get messages slice efficiently (newest first)
    start_idx = max(0, total_messages - skip - limit)
    end_idx = total_messages - skip

    if start_idx >= end_idx:
        messages_slice = []
    else:
        messages_slice = chat.messages[start_idx:end_idx]
        # Reverse to get newest first
        messages_slice = list(reversed(messages_slice))

    # Process messages with minimal overhead
    processed_messages = []
    for message in messages_slice:
        processed_message = message.copy()

        # Only decrypt if necessary and not E2E encrypted
        if message.get('encrypted', False) and not message.get('e2e_encrypted', False):
            try:
                decrypted_content = FriendChat.decrypt_message(
                    message['content'],
                    chat.chat_id
                )
                processed_message['content'] = decrypted_content
                processed_message['encrypted'] = False
            except Exception as e:
                logging.error(f"Error decrypting message: {str(e)}")
                processed_message['content'] = "[Encrypted message - Unable to decrypt]"
                processed_message['decryption_error'] = True
        elif message.get('e2e_encrypted', False):
            # Mark for client-side decryption
            processed_message['needs_client_decryption'] = True

        processed_messages.append(processed_message)

    return {
        'messages': processed_messages,
        'total_messages': total_messages,
        'has_more': skip + limit < total_messages
    }


@friends_api.route('/cleanup', methods=['POST'])
@login_required
def cleanup_chats():
    """Clean up stale chats (admin only)"""
    # Check if user is admin
    if not current_user.is_admin:
        return jsonify({'error': 'Unauthorized'}), 403

    # Clean up stale chats
    count = FriendChat.cleanup_stale_chats()

    return jsonify({
        'success': True,
        'message': f'Cleaned up {count} stale chats'
    })